import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

export default defineConfig({
  plugins: [react()],
  css: {
    preprocessorOptions: {
      css: {
        additionalData: `@import "./src/index.css";`
      }
    }
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    port: 5173, // Run Vite on default port
    proxy: {
      "/api": {
        target: "http://localhost:3000", // Proxy API requests to backend
        changeOrigin: true,
        secure: false,
      },
    },
  }
});

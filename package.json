{"name": "chatbot", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@rsuite/icons": "^1.3.2", "@xyflow/react": "^12.4.4", "antd": "^5.24.3", "axios": "^1.8.1", "cors": "^2.8.5", "cytoscape": "^3.31.1", "cytoscape-node-html-label": "^1.2.2", "emoji-picker-react": "^4.12.2", "express": "^4.21.2", "lucide-react": "^0.476.0", "marked": "^15.0.8", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-datepicker": "^8.3.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-feather": "^2.0.10", "react-flow-renderer": "^10.3.17", "react-icons": "^5.5.0", "react-resizable": "^3.0.5", "react-router-dom": "^7.2.0", "react-syntax-highlighter": "^5.8.0", "react-toastify": "^11.0.5", "reactflow": "^11.11.4", "rss-parser": "^3.13.0", "rsuite": "^5.80.1", "rsuite-table": "^5.19.1"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-react-swc": "^3.8.0", "autoprefixer": "^10.4.20", "eslint": "^9.21.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.3", "tailwindcss": "^4.0.9", "vite": "^6.2.0", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.0"}}
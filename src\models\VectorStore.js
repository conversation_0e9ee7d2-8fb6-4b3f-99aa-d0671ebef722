export class VectorStore {
    constructor({ id, vectorStoreId, name, assistantVectorStores, documents = [], embeddings = [] }) {
        this.id = id;
        this.vectorStoreId = vectorStoreId;
        this.name = name;
        this.assistantVectorStores = assistantVectorStores;
        this.documents = documents;
        this.embeddings = embeddings;
    }

    addDocument(document) {
        this.documents.push(document);
    }

    removeDocument(documentId) {
        this.documents = this.documents.filter(doc => doc.id !== documentId);
    }

    getDocuments() {
        return this.documents;
    }

    addEmbedding(documentId, embedding) {
        this.embeddings.push({ documentId, embedding });
    }

    getEmbeddings() {
        return this.embeddings;
    }
}

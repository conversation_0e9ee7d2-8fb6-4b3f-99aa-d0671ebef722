import React from "react";
import "../assets/Style/CustomNode.css";

export default function CustomNode({ id, data, selected }) {
  const handleDrop = (event, field) => {
    event.preventDefault();
    const payload = JSON.parse(event.dataTransfer.getData("application/reactflow/component"));
    data.onDrop(id, field, payload.label);
  };

  return (
    <div className="custom-node">
      <div className="node-title-bar">{data.label}</div>

      {data.label === "RoundRobin Team" && (
        <>
          <p className="node-description">
            A single AssistantAgent (with a calculator tool) in a RoundRobinGroupChat team.
          </p>
          <div className="node-section">
            <div className="section-header">AGENTS ({data.agents.length})</div>
            {data.agents.map((a, i) => <div key={i} className="node-item">{a}</div>)}
            <div className="drop-zone" onDrop={(e) => handleDrop(e, "agents")} onDragOver={(e) => e.preventDefault()}>
              Drop agents here
            </div>
          </div>
          <div className="node-section">
            <div className="section-header">TERMINATIONS</div>
            {data.terminations.map((t, i) => <div key={i} className="node-item">{t}</div>)}
            <div className="drop-zone" onDrop={(e) => handleDrop(e, "terminations")} onDragOver={(e) => e.preventDefault()}>
              Drop termination here
            </div>
          </div>
        </>
      )}

      {data.label === "AssistantAgent" && (
        <>
          <p className="node-description">
            An agent that provides assistance with ability to use tools.
          </p>
          <div className="node-section">
            <div className="section-header">MODEL</div>
            {data.model && <div className="node-item">{data.model}</div>}
            <div className="drop-zone" onDrop={(e) => handleDrop(e, "model")} onDragOver={(e) => e.preventDefault()}>
              Drop model here
            </div>
          </div>
          <div className="node-section">
            <div className="section-header">TOOLS</div>
            {data.tools.map((t, i) => <div key={i} className="node-item">{t}</div>)}
            <div className="drop-zone" onDrop={(e) => handleDrop(e, "tools")} onDragOver={(e) => e.preventDefault()}>
              Drop tools here
            </div>
          </div>
        </>
      )}
    </div>
  );
}

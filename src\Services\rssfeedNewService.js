import Parser from "rss-parser";

const fetchLatestNews = async () => {
    try {
      console.log("🔄 Fetching Latest News...");
  
      // Fetch RSS feed as XML
      const response = await fetch("https://www.breakingtravelnews.com/feeds/");
      const xmlText = await response.text();
  
      // Convert XML to a JavaScript DOM Object
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(xmlText, "text/xml");
  
      // Extract articles from RSS
      const items = xmlDoc.querySelectorAll("item");
      const newsArticles = [];
  
      items.forEach((item, index) => {
        if (index < 3) { // ✅ Get only the latest 3 articles
          newsArticles.push({
            title: item.querySelector("title")?.textContent || "No title",
            link: item.querySelector("link")?.textContent || "#",
            contentSnippet: item.querySelector("description")?.textContent || "No description",
            pubDate: item.querySelector("pubDate")?.textContent || new Date().toISOString(),
          });
        }
      });
  
      console.log("✅ News Fetched:", newsArticles);
      return newsArticles;
    } catch (error) {
      console.error("❌ News Fetching Error:", error);
      return []; // Return an empty array on error
    }
  };
  
  export default fetchLatestNews;
  

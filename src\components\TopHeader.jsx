import React from "react";
import { useNavigate } from "react-router-dom";
import { Search, Plus } from "lucide-react";

export default function TopHeader() {
  const navigate = useNavigate();

  return (
    <div className="top-header">
      <div className="header-left">
        <h1 className="header-title">Apps</h1>
        <div className="header-search">
          <Search size={18} className="search-icon" />
          <input type="text" placeholder="Search apps..." className="search-input" />
        </div>
      </div>
      <div className="header-controls">
        <button className="new-app-btn" onClick={() => navigate("/new-form")}>
          <Plus size={18} className="icon" /> New App
        </button>
      </div>
    </div>
  );
}

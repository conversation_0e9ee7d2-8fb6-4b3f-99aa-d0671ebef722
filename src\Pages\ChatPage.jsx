import React, { useState, useEffect, useRef } from "react";
import { Avatar, SelectPicker } from "rsuite";
import { Send, Shuffle, Globe, Paperclip, Mic } from "lucide-react";
import { useLocation } from "react-router-dom";
import { createThread, sendMessageToThread } from "../Services/api";
import SyntaxHighlighter from "react-syntax-highlighter";
import "../assets/Style/ChatPage.css";
import WechatIcon from "@rsuite/icons/Wechat";
import ChatpageEnhanced from '../components/ChatpageEnhanced';

const DEFAULT_ASSISTANTS = [
  {
    label: "Assistant One (gpt-3.5)",
    value: "assistant_1",
    model: "gpt-3.5",
    provider: "openai",
    assistantId: "assistant_1",
  },
  {
    label: "Assistant Two (gpt-4)",
    value: "assistant_2",
    model: "gpt-4",
    provider: "openai",
    assistantId: "assistant_2",
  },
];

const mockFlights = [
  { id: 1, airline: "United Airlines", duration: "7 hr", price: "$800", departure: "8:00 AM", arrival: "3:00 PM", route: "SFO–LHR" },
  { id: 2, airline: "British Airways", time: "10:00 AM – 5:00 PM", duration: "7 hr", price: "$950", route: "SFO–LHR" },
  { id: 3, airline: "American Airlines", time: "7:00 PM – 11:00 AM", duration: "16 hr", price: "$750", route: "SFO–LHR" },
  { id: 4, airline: "Delta Air Lines", time: "9:00 PM – 1:00 PM", duration: "16 hr", price: "$880", route: "SFO–LGW" },
];


const seatMap = [
  ["$60", "$60", "$60", "", "$70", "$70", "$50"],
  ["$60", "$60", "$60", "", "$70", "$70", "$50"],
  ["$60", "$60", "$60", "", "$70", "$70", "$50"],
  ["$60", "$60", "$60", "", "$70", "$70", "$50"],
  ["$60", "$60", "$60", "", "$70", "$70", "$50"]
];

const unavailableSeats = new Set(["A3", "C1", "B5", "E3"]); // Example blocked seats





export default function ChatPage() {
  const location = useLocation();
  // Only load history if explicitly requested.
  const loadHistory = location.state?.loadHistory;
  // If loadHistory is true, initialize with the provided chatSession; otherwise, start with an empty chat.
  const initialChatSession = loadHistory ? (location.state?.chatSession || []) : [];
  const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
  const [isListening, setIsListening] = useState(false);
  // Even if location.state exists, on refresh (or when no history flag is provided) we start empty.
  const [messages, setMessages] = useState(initialChatSession);
  const [userInput, setUserInput] = useState("");
  const messagesEndRef = useRef(null);
  const chatMessagesRef = useRef(null);

  // Add state for scroll-to-bottom functionality
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [isUserScrolling, setIsUserScrolling] = useState(false);

  const [isSpeaking, setIsSpeaking] = useState(false); // Track AI speech
  const [speechEnabled, setSpeechEnabled] = useState(true); // Toggle AI voice reply

  const availableAssistantsFromState = location.state?.availableAssistants;
  const availableAssistants = availableAssistantsFromState || DEFAULT_ASSISTANTS;
  const assistantsForDropdown = availableAssistants.map((assistant) => ({
    label: assistant.label || assistant.name,
    value: assistant.value || assistant.id,
    ...assistant,
  }));
  const initialAssistantFromState = location.state?.assistant;
  const initialAssistant = initialAssistantFromState
    ? {
        label: initialAssistantFromState.label || initialAssistantFromState.name,
        value: initialAssistantFromState.value || initialAssistantFromState.id,
        ...initialAssistantFromState,
      }
    : assistantsForDropdown[0];
  const [selectedAssistantId, setSelectedAssistantId] = useState(initialAssistant.value);
  const currentAssistant = assistantsForDropdown.find(
    (assistant) => assistant.value === selectedAssistantId
  );
  const [threadId, setThreadId] = useState(null);

  // Local flag to avoid saving duplicate history.
  const [isSaved, setIsSaved] = useState(false);

  const quickActions = [
    { 
      title: "Help me book a flight", 
      subtitle: "from San Francisco to London", 
      prompt: "Help me book a flight from San Francisco to London" 
    },
    { 
      title: "What is the status", 
      subtitle: "of flight BA142 flying tmrw?", 
      prompt: "What is the status of flight BA142 flying tomorrow?" 
    },
  ];

  const handleSeatSelect = (seatId) => {
    setMessages((prev) => [
      ...prev,
      { sender: "User", text: `I'd like to go with seat ${seatId}` }
    ]);
  
    setTimeout(() => {
      setMessages((prev) => [
        ...prev,
        { sender: "AI", text: "And the passenger name please?", isAwaitingPassengerName: true }
      ]);
    }, 1000);
  };
  
  const sendQuickMessage = (prompt) => {
    if (!prompt) return;
    const isFlightQuery = prompt.toLowerCase().includes("book a flight");
    sendMessage(prompt, isFlightQuery); // ✅ No duplicate message
};

  
  
  const handleFlightSelect = (flight) => {
    // Add user message confirming flight selection
    setMessages((prev) => [
      ...prev,
      { sender: "User", text: `I would like to book the ${flight.airline} one!` },
    ]);
  
    // Add AI response with delay to simulate processing
    setTimeout(() => {
      setMessages((prev) => [
        ...prev,
        { sender: "AI", text: "Here is the available seating. Please choose your seat.", isSeatSelection: true }
      ]);
    }, 1000); // 1-second delay before showing seats
  };
  
  // Reset conversation if not loading history.
  useEffect(() => {
    // If no history flag is passed, force a new chat.
    if (!location.state || !location.state.loadHistory) {
      setMessages([]);
      setThreadId(null);
      setIsSaved(false);
    }
  }, [location.key]);

  // NEW: Update messages if a history session is loaded.
  useEffect(() => {
    if (location.state?.loadHistory) {
      setMessages(location.state.chatSession || []);
      setIsSaved(true); // prevent re-saving history-loaded sessions
    }
  }, [location.state]);

  // Compute total word count.
  const getTotalWordCount = (msgs) =>
    msgs.map((msg) => (msg.text ? msg.text.split(/\s+/).length : 0)).reduce((a, b) => a + b, 0);

  // Save chat session if total words exceed threshold.
  const saveChatSession = () => {
    // Do not re-save if we are loading a history session.
    if (location.state?.loadHistory) return;
    if (messages.length === 0 || isSaved) return;
    const totalWords = getTotalWordCount(messages);
    if (totalWords > 10) { // adjust threshold as needed
      let chatHistory = JSON.parse(localStorage.getItem("chatHistory")) || [];
      const newHistoryItem = {
        messages, // Save the full conversation
        id: `chat_${Date.now()}`,
        preview: messages[0]?.text?.substring(0, 30) || "New Chat",
      };
      chatHistory.push(newHistoryItem);
      localStorage.setItem("chatHistory", JSON.stringify(chatHistory));
      setIsSaved(true);
      // Dispatch custom event for Sidebar update.
      window.dispatchEvent(new CustomEvent("chatHistoryUpdated"));
    }
  };

  // Save conversation when messages update.
  useEffect(() => {
    if (getTotalWordCount(messages) > 10) {
      saveChatSession();
    }
  }, [messages]);

  // Save session on unmount.
  useEffect(() => {
    return () => {
      saveChatSession();
    };
  }, []);

  // Create new thread if starting with an empty conversation.
  useEffect(() => {
    if (!currentAssistant) {
      console.error("❌ No assistant selected!");
      return;
    }
    if (messages.length === 0) {
      // Add welcome message for new sessions
      setMessages([
        { sender: "AI", text: "Hi I am virgino bot, how could I help you today?" }
      ]);
      createThread()
        .then((id) => setThreadId(id))
        .catch((error) => console.error("❌ Error initializing thread:", error));
    }
  }, [currentAssistant, messages]);

  // Check if content is scrollable and user is near bottom
  const isNearBottom = () => {
    if (!chatMessagesRef.current) return true;
    const { scrollTop, scrollHeight, clientHeight } = chatMessagesRef.current;

    // First check if content is actually scrollable
    const isScrollable = scrollHeight > clientHeight;
    if (!isScrollable) return true; // If not scrollable, consider as "at bottom"

    return scrollHeight - scrollTop - clientHeight < 100; // 100px threshold
  };

  // Check if content exceeds visible area (is scrollable)
  const isContentScrollable = () => {
    if (!chatMessagesRef.current) return false;
    const { scrollHeight, clientHeight } = chatMessagesRef.current;
    return scrollHeight > clientHeight;
  };

  // Handle scroll events
  const handleScroll = () => {
    if (!chatMessagesRef.current) return;

    const isScrollable = isContentScrollable();
    const nearBottom = isNearBottom();

    // Only show button if content is scrollable AND user is not near bottom
    setShowScrollToBottom(isScrollable && !nearBottom);

    // If user scrolled manually, mark as user scrolling
    if (!nearBottom && isScrollable) {
      setIsUserScrolling(true);
    } else {
      setIsUserScrolling(false);
    }
  };

  // Scroll to the latest message (only if user hasn't scrolled up)
  useEffect(() => {
    if (!isUserScrolling) {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }
    // Also check if we need to update button visibility when messages change
    setTimeout(() => {
      handleScroll();
    }, 100); // Small delay to ensure DOM is updated
  }, [messages]);

  // Add scroll event listener
  useEffect(() => {
    const chatMessages = chatMessagesRef.current;
    if (chatMessages) {
      chatMessages.addEventListener('scroll', handleScroll);
      return () => chatMessages.removeEventListener('scroll', handleScroll);
    }
  }, []);

  // Handle scroll to bottom button click
  const handleScrollToBottomClick = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    setIsUserScrolling(false);
    setShowScrollToBottom(false);
  };

  const handleMagicWordSubmit = (event) => {
    if (event.key === "Enter") {
        event.preventDefault();
        const input = userInput.trim().toLowerCase();

        if (input === "vercel") { // ✅ Correct magic word
            setMessages((prev) => [
                ...prev,
                { sender: "User", text: userInput }, // ✅ Show User Input
                { sender: "AI", text: "✅ Payment Verified", isPaymentVerified: true }, // ✅ Payment Verified Card
                { sender: "AI", isBoardingPass: true, boardingDetails: {
                    departure: "San Francisco",
                    destination: "London",
                    departureCode: "SFO",
                    destinationCode: "LHR",
                    passenger: "Nitin Sinha",  // ✅ Update dynamically if needed
                    gate: "A3",
                    boardingTime: "7:00PM"
                }},
                { sender: "AI", text: "Here is your boarding pass. Have a great flight!" } // ✅ Final Message
            ]);
        } else {
            setMessages((prev) => [
                ...prev,
                { sender: "User", text: userInput }, // ✅ Show User Input
                { sender: "AI", text: "❌ Incorrect magic word. Please try again." } // ❌ Error Message
            ]);
        }

        setUserInput(""); // ✅ Clear Input Box
    }
};

const speakText = (text) => {
  if (!speechEnabled) return; // Skip if disabled

  const utterance = new SpeechSynthesisUtterance(text);
  utterance.lang = "en-US"; // Adjust for different languages
  utterance.volume = 1; 
  utterance.rate = 1; 
  utterance.pitch = 1;

  utterance.onstart = () => setIsSpeaking(true);
  utterance.onend = () => setIsSpeaking(false);

  window.speechSynthesis.speak(utterance);
};

  
  
   
  // Send message function.
  const sendMessage = async (message = "", isFlightOptions = false) => {
    const currentInput = typeof message === "string" ? message.trim() : String(userInput).trim(); // Ensure input is a string
    if (!currentInput) return;
    setUserInput("");
    setMessages((prev) => [...prev, { sender: "User", text: currentInput }]);

    const lastAIMessage = messages[messages.length - 1];
    // if (!lastAIMessage?.isAwaitingPassengerName) {
    // setMessages((prev) => [...prev, { sender: "AI", text: "...", isFlightOptions: isFlightOptions }]);
    // }
    if (!lastAIMessage?.isAwaitingPassengerName && !isFlightOptions) {
      setMessages((prev) => [...prev, { sender: "AI", text: "..." }]);
    }
    if (isFlightOptions) {
      setTimeout(() => {
        setMessages((prev) => [
          ...prev,
          { sender: "AI", text: "Here are some available flights:", isFlightOptions: true }
        ]);
      }, 1000);
      return;
    }
   

  // Handle Passenger Name Entry
  if (lastAIMessage?.isAwaitingPassengerName) {
    setTimeout(() => {
      setMessages((prev) => [
        ...prev,
        {
          sender: "AI",
          isReservationSummary: true,
          reservationDetails: {
            passenger: currentInput,
            flightNumber: "BA123",
            route: "London to Delhi",
            price: "$1.23 USD",
            date: "10 May 2024",
            seat: "3C",
          }
        },
        { sender: "AI", text: "OK. I have created a reservation with the following details. Should I proceed with the payment?" }
      ]);
    }, 1000);
    return;
  }

  // Handle Payment Confirmation Flow
  const positiveResponses = ["yes", "proceed", "confirm", "sure", "okay", "go ahead"];
  if (positiveResponses.some((word) => currentInput.toLowerCase().includes(word))) {
    setTimeout(() => {
      setMessages((prev) => [
        ...prev,
        {
          sender: "AI",
          isPaymentAuthorization: true,  // ✅ Flag to render payment card
        },
        { sender: "AI", text: "Please authorize the payment. Let me know when you are done." }
      ]);
    }, 1000);
    return;
  }

  if (isListening) {
    speakText(streamedText);
  }

    try {
     
      const responseStream = sendMessageToThread({
        threadId,
        assistantId: currentAssistant.assistantId,
        temperature: 0.7,
        message: currentInput,
      });
      let streamedText = "";
      for await (const partialResponse of responseStream) {
        streamedText = partialResponse;
        setMessages((prev) => [
          ...prev.slice(0, -1),
          { sender: "AI", text: streamedText.trim(), isFlightOptions },
        ]);
      }
    } catch (error) {
      console.error("❌ Streaming error:", error);
      setMessages((prev) => [
        ...prev.slice(0, -1),
        { sender: "AI", text: "⚠️ AI response failed." },
      ]);
    }
  };

  const startListening = () => {
    const recognition = new window.webkitSpeechRecognition() || new window.SpeechRecognition();
    recognition.lang = "en-US"; // Adjust for different languages
    recognition.continuous = false;
    recognition.interimResults = false;
  
    recognition.onstart = () => setIsListening(true);
    recognition.onend = () => setIsListening(false);
  
    recognition.onresult = (event) => {
      const transcript = event.results[0][0].transcript;
      setUserInput(transcript);
      sendMessage(transcript); // Automatically send voice input
    };
  
    recognition.start();
  };
  

  return (
    <div className="app-container">
      <div className="main-content">
        {/* Chat Box */}
        <div className="chat-box">
          {/* Chat Messages */}
          <div className="chat-messages" ref={chatMessagesRef}>
            {messages.length === 0 ? (
              <h3 className="empty-chat">
                <WechatIcon /> Start a conversation...
              </h3>
            ) : (
              messages.map((msg, index) => (
                <div
                  key={index}
                  className={`chat-message ${msg.sender === "User" ? "user-message" : "ai-message"}`}
                >
                  <Avatar
                    circle
                    size="md"
                    src={
                      msg.sender === "User"
                        ? "https://placekitten.com/60/60"
                        : "https://placehold.co/60x60/blue/white?text=AI"
                    }
                  />
                <div className={`chat-bubble ${msg.sender === "User" ? "user-bubble" : "ai-bubble"}`}>
                    <b>{msg.sender}</b>
                    {msg.isCode ? (
                     // <SyntaxHighlighter language={msg.language || "plaintext"} style={atomOneDark}></SyntaxHighlighter>
                      <SyntaxHighlighter language={msg.language || "plaintext"}>
                        {msg.text}
                      </SyntaxHighlighter>
                    ) : msg.isFlightOptions ? (
                      <div className="flights-container">
                        {mockFlights.map((flight) => (
                          <div key={flight.id} className="flight-card" onClick={() => handleFlightSelect(flight)}>
                            <div className="flight-header">
                              <b>{flight.time}</b>
                              <span className="flight-duration">{flight.duration}</span>
                              <span className="flight-price">{flight.price}</span>
                            </div>
                            <div className="flight-details">
                              <span className="flight-airline">{flight.airline}</span>
                              <span className="flight-route">{flight.route} Round Trip</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : msg.isSeatSelection ? (
                      <div className="seat-selection-container">
                        <table className="seat-map">
                          <thead>
                            <tr>
                              <th></th> {/* Empty for alignment */}
                              {["A", "B", "C", "", "D", "E", "F"].map((col, index) => (
                                <th key={index} className="seat-header">{col}</th>
                              ))}
                            </tr>
                          </thead>
                          <tbody>
                            {seatMap.map((row, rowIndex) => (
                              <tr key={rowIndex}>
                                <td className="seat-row-label">{rowIndex + 1}</td>
                                {row.map((seat, colIndex) => {
                                  const seatId = String.fromCharCode(65 + colIndex) + (rowIndex + 1); // A1, B2, etc.
                                  return (
                                    <td
                                      key={colIndex}
                                      className={`seat ${unavailableSeats.has(seatId) ? "unavailable" : "available"}`}
                                      onClick={() => !unavailableSeats.has(seatId) && handleSeatSelect(seatId)}
                                    >
                                      {seat}
                                    </td>
                                  );
                                })}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                        <div className="seat-legend">
                          <span className="legend available"></span> Available
                          <span className="legend unavailable"></span> Unavailable
                        </div>
                      </div>
                    ) :msg.isReservationSummary ? (
                      <div className="reservation-summary">
                        <p>
                          Continue purchasing this reservation from <b>{msg.reservationDetails.route}</b> at <span className="flight-price">{msg.reservationDetails.price}</span>?
                        </p>
                        <table>
                          <thead>
                            <tr>
                              <th>Seats</th>
                              <th>Flight Number</th>
                              <th>Date</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td>{msg.reservationDetails.seat}</td>
                              <td>{msg.reservationDetails.flightNumber}</td>
                              <td>{msg.reservationDetails.date}</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    ): msg.isPaymentAuthorization ? (
                      <div className="payment-auth-container">
                        <div className="payment-card">
                          <b className="payment-title">💳 Use your saved information for this transaction</b>
                          <p className="payment-subtext">Enter the magic word to authorize payment. Hint: It rhymes with <b>bercel</b>.</p>
                          
                          {/* Magic Word Input Box */}
                          <input
                          type="text"
                          className="magic-word-input"
                          placeholder="Enter magic word..."
                          value={userInput} // ✅ Ensure state updates
                          onChange={(e) => setUserInput(e.target.value)} // ✅ Keep state updated
                          onKeyDown={(e) => handleMagicWordSubmit(e)} // ✅ Attach event handler
                        />
                        </div>
                    
                        {/* Below Text after Input */}
                        <p className="payment-footer">Please authorize the payment. Let me know when you are done.</p>
                      </div>
                    ) :msg.isBoardingPass ? (
                      <div className="boarding-pass">
                        <div className="boarding-header">
                          <span>{msg.boardingDetails.departure}</span>
                          <span className="flight-icon">✈️</span>
                          <span>{msg.boardingDetails.destination}</span>
                        </div>
                        <div className="boarding-main">
                          <span className="boarding-code">{msg.boardingDetails.departureCode}</span>
                          <span className="boarding-code">{msg.boardingDetails.destinationCode}</span>
                        </div>
                        <div className="boarding-info">
                          <div><b>Passenger</b> <span>{msg.boardingDetails.passenger}</span></div>
                          <div><b>Gate</b> <span>{msg.boardingDetails.gate}</span></div>
                          <div><b>Boards</b> <span>{msg.boardingDetails.boardingTime}</span></div>
                        </div>
                      </div>
                    ) :msg.isPaymentVerified ? (
                      <div className="payment-verified">
                        <span>✅ Payment Verified</span>
                        <span className="check-icon">✔</span>
                      </div>
                    ):(
                      <p>{msg.text}</p>
                    )}
                  </div>
                </div>
              ))
            )}
            <div ref={messagesEndRef}></div>

            {/* Scroll to Bottom Button */}
            {showScrollToBottom && (
              <div className="scroll-to-bottom-container">
                <button
                  className="scroll-to-bottom-btn"
                  onClick={handleScrollToBottomClick}
                  title="Scroll to bottom"
                >
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M7 13l3 3 3-3"></path>
                    <path d="M7 6l3 3 3-3"></path>
                  </svg>
                </button>
              </div>
            )}
          </div>
          
          <div className="quick-actions-links-container">
              {quickActions.map((action, idx) => (
                <div
                  key={idx}
                  className="quick-link-card"
                  onClick={() => sendQuickMessage(`${action.title} ${action.subtitle}`)}
                >
                  <div className="quick-link-title">{action.title}</div>
                  <div className="quick-link-subtitle">{action.subtitle}</div>
                </div>
              ))}
              <ChatpageEnhanced setMessages={setMessages} />
           </div>

          {/* Chat Input Section */}
          <div className="chat-input-box">
            <button className="icon-btn">
              <Shuffle size={20} />
            </button>
            <textarea
              className="message-box"
              placeholder="Ask anything..."
              value={userInput}
              onChange={(e) => setUserInput(e.target.value)}
            ></textarea>
            <SelectPicker
              data={assistantsForDropdown}
              value={selectedAssistantId}
              onChange={(value) => setSelectedAssistantId(value)}
              style={{ width: 150 }}
              placeholder="Select Assistant"
            />
           <button className="icon-btn" onClick={startListening}>
              <Mic size={20} color={isListening ? "red" : "black"} /> {/* Red while listening */}
            </button>
            <button className="icon-btn">
              <Globe size={20} />
            </button>
            <button className="icon-btn">
              <Paperclip size={20} />
            </button>
            <button className="send-btn" onClick={sendMessage}>
              <Send size={20} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}



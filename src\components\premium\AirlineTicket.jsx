import React, { useState, useRef } from 'react';
import '../../assets/Style/PremiumStyles.css';

const AirlineTicket = ({ ticketData, className }) => {
  const [isHovered, setIsHovered] = useState(false);
  const ticketRef = useRef(null);
  
  // Use ticketData if provided, otherwise use default values
  const ticket = ticketData || {
    flightNumber: "VS204",
    departureCode: "LHR",
    departureName: "London Heathrow",
    departureTime: "10:45",
    departureDate: "2024-06-15",
    arrivalCode: "JFK",
    arrivalName: "New York JFK",
    arrivalTime: "13:50",
    arrivalDate: "2024-06-15",
    passengerName: "<PERSON>",
    ticketClass: "Upper Class",
    seat: "3A",
    gate: "B22",
    boardingTime: "10:00",
    price: "£2,450",
    status: "Confirmed",
    pnr: "ABC123",
    miles: "3,458"
  };

  // Format date to display in a more compact format
  const formatDate = (dateStr) => {
    if (!dateStr) return '';
    try {
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return dateStr;
      
      return date.toLocaleDateString('en-GB', { 
        day: 'numeric', 
        month: 'short'
      });
    } catch (e) {
      return dateStr;
    }
  };

  // Calculate flight duration
  const getDuration = () => {
    try {
      const [depHours, depMinutes] = ticket.departureTime.split(':').map(Number);
      const [arrHours, arrMinutes] = ticket.arrivalTime.split(':').map(Number);
      
      let durationMinutes = (arrHours * 60 + arrMinutes) - (depHours * 60 + depMinutes);
      if (durationMinutes < 0) durationMinutes += 24 * 60;
      
      const hours = Math.floor(durationMinutes / 60);
      const minutes = durationMinutes % 60;
      
      return `${hours}h${minutes > 0 ? ` ${minutes}m` : ''}`;
    } catch (e) {
      return "N/A";
    }
  };

  return (
    <div 
      className={`va-compact-ticket ${className || ''}`}
      ref={ticketRef}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{
        transform: isHovered ? 'translateY(-3px)' : 'translateY(0)',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        boxShadow: isHovered ? '0 10px 20px rgba(0,0,0,0.12)' : '0 3px 10px rgba(0,0,0,0.08)'
      }}
    >
      {/* Main Ticket Container */}
      <div className="va-compact-container">
        {/* Header with Logo and Flight Info */}
        <div className="va-compact-header">
          <div className="va-compact-logo">
            <span className="va-logo-icon">✈</span>
            <span className="va-logo-text">VIRGIN ATLANTIC</span>
          </div>
          <div className="va-compact-flight">
            <div className="va-compact-flight-number">{ticket.flightNumber}</div>
            <div className="va-compact-flight-class">{ticket.ticketClass}</div>
          </div>
        </div>
        
        {/* Route Information */}
        <div className="va-compact-route">
          <div className="va-compact-departure">
            <div className="va-compact-airport-code">{ticket.departureCode}</div>
            <div className="va-compact-airport-name">{ticket.departureName}</div>
            <div className="va-compact-time">{ticket.departureTime}</div>
            <div className="va-compact-date">{formatDate(ticket.departureDate)}</div>
          </div>
          
          <div className="va-compact-flight-path">
            <div className="va-compact-duration">{getDuration()}</div>
            <div className="va-compact-path-line">
              <div className="va-compact-plane-icon">✈</div>
            </div>
          </div>
          
          <div className="va-compact-arrival">
            <div className="va-compact-airport-code">{ticket.arrivalCode}</div>
            <div className="va-compact-airport-name">{ticket.arrivalName}</div>
            <div className="va-compact-time">{ticket.arrivalTime}</div>
            <div className="va-compact-date">{formatDate(ticket.arrivalDate)}</div>
          </div>
        </div>
        
        {/* Passenger Details */}
        <div className="va-compact-details">
          <div className="va-compact-detail-item">
            <span className="va-compact-label">PASSENGER</span>
            <span className="va-compact-value">{ticket.passengerName}</span>
          </div>
          
          <div className="va-compact-detail-row">
            <div className="va-compact-detail-item">
              <span className="va-compact-label">SEAT</span>
              <span className="va-compact-value">{ticket.seat}</span>
            </div>
            
            <div className="va-compact-detail-item">
              <span className="va-compact-label">GATE</span>
              <span className="va-compact-value">{ticket.gate}</span>
            </div>
            
            <div className="va-compact-detail-item">
              <span className="va-compact-label">BOARDING</span>
              <span className="va-compact-value">{ticket.boardingTime}</span>
            </div>
          </div>
        </div>
        
        {/* Footer with PNR and Status */}
        <div className="va-compact-footer">
          <div className="va-compact-pnr">
            <span className="va-compact-label">PNR</span>
            <span className="va-compact-pnr-value">{ticket.pnr}</span>
          </div>
          
          <div className="va-compact-status">
            {ticket.status}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AirlineTicket;



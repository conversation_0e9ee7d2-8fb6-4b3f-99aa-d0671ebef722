import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, Divider } from 'rsuite';
import { Clock, MapPin, Calendar, User, Briefcase, Scissors } from 'react-feather';
import QRCode from 'qrcode.react';
import './PremiumStyles.css';

const BoardingPass = ({ boardingData, onDownload, className }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const boardingPassRef = useRef(null);
  
  // Default boarding data if none provided
  const defaultBoarding = {
    airline: "Virgin Atlantic",
    flightNumber: "VS206",
    passengerName: "EMMA THOMPSON",
    departureCode: "LHR",
    departureName: "London Heathrow",
    departureTerminal: "3",
    departureTime: "10:45",
    departureDate: "2024-06-15",
    arrivalCode: "JFK",
    arrivalName: "New York JFK",
    arrivalTerminal: "4",
    gate: "B22",
    boardingTime: "10:00",
    seat: "3A",
    seatZone: "1",
    ticketClass: "Upper Class",
    boardingGroup: "1",
    boardingSequence: "001",
    pnr: "ABC123",
    barcode: "M1THOMPSON/EMMA       EABC123 LHRJFKVS 0206 166Y003A0188 100"
  };
  
  // Use provided data or defaults
  const boarding = boardingData || defaultBoarding;
  
  // Format date to display in a more readable format
  const formatDate = (dateStr) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-GB', { 
      weekday: 'short', 
      day: 'numeric', 
      month: 'short'
    });
  };
  
  // Format time to display in a more readable format
  const formatTime = (timeStr) => {
    return timeStr;
  };
  
  // Handle download action
  const handleDownload = () => {
    if (onDownload) {
      onDownload(boarding);
    } else {
      // Default download behavior
      alert("Download functionality would be implemented here");
    }
  };
  
  return (
    <div 
      className={`premium-boarding-pass ${className || ''} ${isExpanded ? 'expanded' : ''}`}
      ref={boardingPassRef}
    >
      <div className="boarding-pass-container">
        <div className="boarding-pass-header">
          <div className="airline-logo">
            <span className="logo-icon">✈</span>
            <span className="airline-name">{boarding.airline}</span>
          </div>
          <div className="boarding-title">BOARDING PASS</div>
        </div>
        
        <div className="boarding-pass-main">
          <div className="boarding-pass-flight">
            <div className="flight-number">{boarding.flightNumber}</div>
            <div className="flight-date">{formatDate(boarding.departureDate)}</div>
          </div>
          
          <div className="boarding-pass-route">
            <div className="departure">
              <div className="city-code">{boarding.departureCode}</div>
              <div className="city-name">{boarding.departureName}</div>
              <div className="terminal">Terminal {boarding.departureTerminal}</div>
              <div className="time">{formatTime(boarding.departureTime)}</div>
            </div>
            
            <div className="route-line">
              <div className="plane-icon">
                <svg viewBox="0 0 24 24" width="24" height="24" stroke="currentColor" strokeWidth="2" fill="none">
                  <path d="M22 12H2M12 2v20M2 7h20M2 17h20"></path>
                </svg>
              </div>
            </div>
            
            <div className="arrival">
              <div className="city-code">{boarding.arrivalCode}</div>
              <div className="city-name">{boarding.arrivalName}</div>
              <div className="terminal">Terminal {boarding.arrivalTerminal}</div>
            </div>
          </div>
          
          <div className="boarding-pass-details">
            <div className="detail-column">
              <div className="detail-item">
                <div className="detail-label">
                  <User size={14} />
                  <span>Passenger</span>
                </div>
                <div className="detail-value">{boarding.passengerName}</div>
              </div>
              
              <div className="detail-item">
                <div className="detail-label">
                  <MapPin size={14} />
                  <span>Seat</span>
                </div>
                <div className="detail-value">{boarding.seat}</div>
              </div>
              
              <div className="detail-item">
                <div className="detail-label">
                  <Briefcase size={14} />
                  <span>Class</span>
                </div>
                <div className="detail-value">{boarding.ticketClass}</div>
              </div>
            </div>
            
            <div className="detail-column">
              <div className="detail-item">
                <div className="detail-label">
                  <Clock size={14} />
                  <span>Boarding</span>
                </div>
                <div className="detail-value">{boarding.boardingTime}</div>
              </div>
              
              <div className="detail-item">
                <div className="detail-label">
                  <MapPin size={14} />
                  <span>Gate</span>
                </div>
                <div className="detail-value">{boarding.gate}</div>
              </div>
              
              <div className="detail-item">
                <div className="detail-label">
                  <Calendar size={14} />
                  <span>Group</span>
                </div>
                <div className="detail-value">{boarding.boardingGroup}</div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="boarding-pass-qr">
          <QRCode 
            value={boarding.barcode || `BOARDING:${boarding.flightNumber}:${boarding.passengerName}:${boarding.seat}`}
            size={120}
            level="H"
            renderAs="svg"
            includeMargin={true}
            className="qr-code"
          />
          <div className="boarding-sequence">
            Seq: {boarding.boardingSequence}
          </div>
        </div>
        
        <div className="boarding-pass-footer">
          <div className="pnr">PNR: {boarding.pnr}</div>
          <div className="boarding-note">
            Please be at the gate at least 30 minutes before departure
          </div>
        </div>
        
        <div className="boarding-pass-actions">
          <Button appearance="primary" onClick={handleDownload}>
            Save to Wallet
          </Button>
          <Button appearance="ghost" onClick={() => setIsExpanded(!isExpanded)}>
            {isExpanded ? 'Show Less' : 'Show More'}
          </Button>
        </div>
        
        {isExpanded && (
          <div className="boarding-pass-expanded">
            <Divider>Additional Information</Divider>
            
            <div className="expanded-content">
              <div className="expanded-section">
                <h4>Baggage Allowance</h4>
                <div className="baggage-details">
                  <div className="baggage-item">
                    <Briefcase size={16} />
                    <span>2 x 23kg Checked Baggage</span>
                  </div>
                  <div className="baggage-item">
                    <Briefcase size={14} />
                    <span>1 x 10kg Cabin Baggage</span>
                  </div>
                </div>
              </div>
              
              <div className="expanded-section">
                <h4>Lounge Access</h4>
                <div className="lounge-details">
                  <p>Virgin Atlantic Clubhouse</p>
                  <p>Terminal {boarding.departureTerminal}, Near Gate A10</p>
                  <p>Open 6:00 AM - 10:00 PM</p>
                </div>
              </div>
              
              <div className="expanded-section">
                <h4>Fast Track Security</h4>
                <div className="fast-track-details">
                  <p>Available for Upper Class passengers</p>
                  <p>Follow Fast Track signs at security</p>
                </div>
              </div>
            </div>
          </div>
        )}
        
        <div className="boarding-pass-tear">
          <div className="tear-line">
            <Scissors size={16} />
            <div className="tear-text">✂ ─────────────────────────────────────── ✂</div>
            <Scissors size={16} />
          </div>
        </div>
        
        <div className="boarding-pass-stub">
          <div className="stub-header">
            <div className="airline-logo-small">
              <span className="logo-icon-small">✈</span>
              <span className="airline-name-small">{boarding.airline}</span>
            </div>
          </div>
          
          <div className="stub-content">
            <div className="stub-flight">
              <div className="stub-label">Flight</div>
              <div className="stub-value">{boarding.flightNumber}</div>
            </div>
            
            <div className="stub-date">
              <div className="stub-label">Date</div>
              <div className="stub-value">{formatDate(boarding.departureDate)}</div>
            </div>
            
            <div className="stub-route">
              <div className="stub-label">Route</div>
              <div className="stub-value">{boarding.departureCode} → {boarding.arrivalCode}</div>
            </div>
            
            <div className="stub-seat">
              <div className="stub-label">Seat</div>
              <div className="stub-value">{boarding.seat}</div>
            </div>
            
            <div className="stub-gate">
              <div className="stub-label">Gate</div>
              <div className="stub-value">{boarding.gate}</div>
            </div>
            
            <div className="stub-boarding">
              <div className="stub-label">Boarding</div>
              <div className="stub-value">{boarding.boardingTime}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BoardingPass;

/* === GENERAL SIDEBAR LAYOUT === */
.visual-builder-wrapper {
    display: flex;
    height: calc(100vh - 60px);
    overflow: hidden;
    margin-top: 60px;
  }
  
  .sidebar {
    width: 280px;
    background: #f9fafb;
    padding: 1rem;
    border-right: 1px solid #e5e7eb;
    overflow-y: auto;
  }
  
  .library-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
  }
  
  .library-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 1rem;
  }
  
  .component-search {
    width: 94%;
    padding: 6px 10px;
    margin-bottom: 1rem;
    border-radius: 6px;
    border: 1px solid #ccc;
    font-size: 0.875rem;
  }
  
  /* === COLLAPSIBLE SECTIONS === */
  .library-section {
    margin-bottom: 1rem;
  }
  
  .section-header {
    display: flex;
    align-items: center;
    font-weight: 500;
    cursor: pointer;
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    padding: 0.6rem 0.75rem;
    border-radius: 6px;
    font-size: 0.95rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    transition: background 0.2s;
  }
  
  .section-header:hover {
    background-color: #f3f4f6;
  }
  
  .collapse-arrow {
    margin-right: 0.5rem;
    font-size: 0.9rem;
  }
  
  .item-count {
    margin-left: auto;
    font-size: 0.8rem;
    color: #6b7280;
  }
  
  /* === SECTION ITEMS === */
  .section-items {
    padding-top: 0.5rem;
  }
  
  .draggable-item {
    display: flex;
    align-items: center;
    padding: 0.45rem 0.75rem;
    margin-bottom: 0.4rem;
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 0.85rem;
    cursor: grab;
    transition: background 0.2s, border 0.2s;
    box-shadow: 0 1px 1.5px rgba(0, 0, 0, 0.04);
  }
  
  .draggable-item:hover {
    background-color: #f9fafb;
    border-color: #d1d5db;
  }
  
  .item-icon {
    margin-right: 0.5rem;
  }
  
  /* === FLOW CANVAS === */
  .flow-canvas {
    flex-grow: 1;
    height: 100%;
    background-color: #fff;
    position: relative;
  }
  
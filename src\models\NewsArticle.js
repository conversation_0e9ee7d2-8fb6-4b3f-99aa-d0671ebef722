export class NewsArticle {
    constructor(title, link, contentSnippet, pubDate) {
      this.title = title;
      this.link = link;
      this.contentSnippet = contentSnippet;
      this.pubDate = new Date(pubDate);
    }
  
    // ✅ Helper Method: Format Date
    getFormattedDate() {
      return this.pubDate.toLocaleDateString();
    }
  
    // ✅ Helper Method: Trim Content Snippet
    getShortDescription(charLimit = 100) {
      return this.contentSnippet.length > charLimit ? this.contentSnippet.slice(0, charLimit) + "..." : this.contentSnippet;
    }
  }
  
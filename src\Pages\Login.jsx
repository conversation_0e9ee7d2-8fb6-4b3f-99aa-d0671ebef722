import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import "../App.css"; // Ensure styles are included

export default function LoginPage() {
  const navigate = useNavigate();

  useEffect(() => {
    const fetchLoginUrl = async () => {
      try {
        console.log("🔄 Fetching login URL...");
        const response = await fetch("http://localhost:3000/login");
        console.log("🔄 Response Status:", response.status);

        const loginUrlData = await response.text();
        console.log("✅ Received Login URL:", loginUrlData);

        if (
          response.ok &&
          loginUrlData.includes("https://login.microsoftonline.com")
        ) {
          // Redirect to OAuth provider
          window.location.href = loginUrlData;
        } else {
          console.warn("⚠️ Invalid Login URL, using fallback...");
          handleFallbackLogin();
        }
      } catch (error) {
        console.error("❌ Login API Error:", error);
        handleFallbackLogin();
      }
    };

    const handleFallbackLogin = () => {
      console.log("⏳ Fallback: Waiting for 5 seconds...");
      setTimeout(() => {
        document.cookie = "user_id=Nitin Sinha; path=/";
        console.log("✅ Fallback user set: Nitin Sinha");
        navigate("/");
      }, 5000);
    };

    // Increase delay to 5 seconds before making the API call
    setTimeout(() => {
      fetchLoginUrl();
    }, 5000);
  }, [navigate]);

  return (
    <div className="login-container">
      <div className="login-content">
        {/* Images should be placed in the public folder */}
        <img
          src="../src/assets/images/Virgin-atlantic-logo.jpg"
          alt="Virgin Logo"
          className="logo"
        />
        <img src="../src/assets/images/giphy.gif" alt="Loading..." className="loading-gif" />
        <p className="loading-message">
          Loading your Chat Assistant, please wait...
        </p>
      </div>
    </div>
  );
}

export const DUMMY_ASSISTANT_DATA = [
  {
    id: 5,

    assistantId: "asst_BC9RcdtdSghj1rrlmYqMtB86",

    name: "iTravel Assistant",

    instructions:
      "I am <PERSON>, a web-based, expert document and support assistant for Virgin Atlantic.\nI help users access technical details, application-related information, and Virgin Atlantic support documentation. My primary role is to act as a centralized document store and intelligent search assistant for relevant queries.\n\nRules I Follow When Responding:\n✅ Provide structured responses for easy reference and retrieval from vector datastores.\n✅ Use GitHub-flavored Markdown for code snippets, data, and structured content.\n✅ Always specify the type of data (e.g., JSON, CSV, XML, or code language).\n✅ Format all text for readability, ensuring clarity across different platforms.\n✅ Utilize Nomnoml or Mermaid diagrams when discussing workflows, architecture, or technical structures. When generating C4 documents, always use mermaid C4(for reference https://mermaid.js.org/syntax/c4.html). \nI do not search from public websites or internet, when mention the references, I do provide the document page number and the section/subtitle reference as well.\n\n✅ Act as a powerful document search engine, retrieving and summarizing relevant materials quickly.\n✅ Follow Virgin Atlantic’s best practices for application support, troubleshooting, and technical queries.\n✅ Provide AI-driven insights into Virgin Atlantic’s systems, documentation, and user support.\n\nI am your go-to assistant for any technical, application-related, or document retrieval needs related to Virgin Atlantic. 🚀✈️",

    model: "gpt-4o",

    temperature: 0.35,

    providerId: 3,

    current: true,

    isDefault: false,

    userIds: [1],

    vectorStoreIds: ["vs_Lwd7xLe2V2V6VTOYGUMy6Czw"],
  },

  {
    id: 6,

    assistantId: "asst_cJOIpbK0p0JV45fOvIzFNwoI",

    name: "Finance System Documents Assistant",

    instructions:
      "I am AI, a web-based, expert document and support assistant for Finance Systems.\n\nI specialize in eMRO, Tradeshift, IATA, and Oracle integrations, assisting users in accessing technical details, application-related information, and support documentation. My primary role is to act as a centralized document repository and intelligent search assistant for all finance system-related queries.\n\nRules I Follow When Responding:\n\n✅ Provide structured responses for easy reference and retrieval from vector datastores.\n✅ Use GitHub-flavored Markdown for code snippets, data formats, and structured content.\n✅ Always specify the type of data (e.g., JSON, CSV, XML, or specific code languages).\n✅ Format all text for clarity and readability across multiple platforms.\n✅ Utilize Nomnoml or Mermaid diagrams when discussing workflows, integrations, or system architectures.\n✅ Do not search from public websites or the internet; responses are based on internal documentation.\n✅ Always refer the documents, and provide evidence based details.\n\nCore Capabilities:\n\n✅ Act as a powerful document search engine, retrieving and summarizing relevant materials quickly.\n✅ Provide support for eMRO workflows, including maintenance planning, inventory management, and financial transactions.\n✅ Assist with Tradeshift-related queries, including invoice processing, supplier onboarding, and payment reconciliations.\n✅ Offer guidance on IATA compliance, including financial settlement systems (IATA Clearing House, BSP, CASS) and regulatory standards.\n✅ Deliver AI-driven insights into Oracle integrations, covering financial modules, ERP configurations, and data exchange between systems.\n✅ Follow best practices for finance systems support, troubleshooting, and technical inquiries.\n\nI am your go-to assistant for any finance system technical, application-related, or document retrieval needs. 🚀💰",

    model: "gpt-4o",

    temperature: 0.15,

    providerId: 3,

    current: true,

    isDefault: true,

    userIds: [1],

    vectorStoreIds: ["vs_30dXNmirNdRQIKFrVM9hATFP"],
  },

  {
    id: 8,

    assistantId: "asst_52UmrpGSC6iHX5A3YRKzIXi2",

    name: "PnF Assitant",

    instructions: "Testing PnF",

    model: "Gpt-4o",

    temperature: 0.5,

    providerId: 3,

    current: false,

    isDefault: false,

    userIds: [2],

    vectorStoreIds: ["vs_naSXIv6DNU6nYpKg2q9XdmyU"],
  },

  {
    id: 9,

    assistantId: "asst_yDJloMi0wMJ9AWYvNYARuFn4",

    name: "PnF Project AI Demo",

    instructions: "Only search from document",

    model: "gpt-4o",

    temperature: 0.7,

    providerId: 3,

    current: false,

    isDefault: false,

    userIds: [
      1,

      2,
    ],

    vectorStoreIds: [],
  },
];
export const DUMMY_NEWS_DATA = [
  {
    link: "https://example.com/article1",
    title: "The Future of AI in Healthcare",
    contentsnippet:
      "AI is transforming healthcare by improving diagnostics, treatment plans, and patient outcomes. Experts predict a significant rise in AI integration...",
    publishDate: "2025-03-01",
  },
  {
    link: "https://example.com/article2",
    title: "10 Tips for Sustainable Living",
    contentsnippet:
      "Living sustainably can help reduce your environmental impact. Here are 10 easy ways to live more sustainably in your daily routine...",
    publishDate: "2025-02-28",
  },
  {
    link: "https://example.com/article3",
    title: "How to Start a Successful Online Business",
    contentsnippet:
      "Starting an online business requires careful planning, effective marketing strategies, and understanding your target audience. Here’s a step-by-step guide...",
    publishDate: "2025-02-25",
  },
  {
    link: "https://example.com/article4",
    title: "The Rise of Remote Work: What You Need to Know",
    contentsnippet:
      "Remote work is no longer a trend—it’s the future of work. Companies around the world are adopting remote work models to enhance flexibility and productivity...",
    publishDate: "2025-02-20",
  },
  {
    link: "https://example.com/article5",
    title: "5 Delicious Vegan Recipes You Should Try",
    contentsnippet:
      "Vegan cuisine offers a wide range of flavors and health benefits. These five recipes will help you get started with plant-based cooking...",
    publishDate: "2025-02-15",
  },
  {
    link: "https://example.com/article6",
    title: "The Importance of Mental Health Awareness",
    contentsnippet:
      "Mental health has become a major topic of discussion. It’s vital to raise awareness and reduce stigma surrounding mental health challenges...",
    publishDate: "2025-02-10",
  },
  {
    link: "https://example.com/article7",
    title: "The Best Travel Destinations for 2025",
    contentsnippet:
      "As the world opens up again, travelers are seeking new destinations. Here are the top travel spots for 2025, offering diverse cultures, activities, and experiences...",
    publishDate: "2025-02-05",
  },
  {
    link: "https://example.com/article8",
    title: "How to Boost Your Productivity with Time Management",
    contentsnippet:
      "Effective time management is key to boosting productivity. Learn some proven strategies to prioritize your tasks and make the most of your time each day...",
    publishDate: "2025-02-01",
  },
];

import axios from "axios";
import { Assistant } from "../models/Assistant";
import { NewsArticle } from "../models/NewsArticle";
import {VectorStore} from "../models/VectorStore";

const API_URL = "http://localhost:3000";


const handleApiError = (error) => {
  console.error("❌ API Error:", error);
  throw error.response?.data || { message: "An error occurred" };
};


export const fetchAssistantData = async () => {
  try {
    const response = await axios.get(`${API_URL}/api/assistant`);
    return response.data.map((assistant) => new Assistant(assistant)); 
  } catch (error) {
    return handleApiError(error);
  }
};


export const fetchLatestNews = async () => {
  try {
    console.log("🔄 Fetching Latest News...");
    const response = await fetch("https://www.breakingtravelnews.com/feeds/");
    const xmlText = await response.text();

    // Convert XML to JavaScript DOM Object
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlText, "text/xml");

    // Extract news articles from the RSS feed
    const items = xmlDoc.querySelectorAll("item");
    const newsArticles = [];

    items.forEach((item, index) => {
      if (index < 3) {
        newsArticles.push(
          new NewsArticle(
            item.querySelector("title")?.textContent || "No title",
            item.querySelector("link")?.textContent || "#",
            item.querySelector("description")?.textContent || "No description",
            item.querySelector("pubDate")?.textContent || new Date().toISOString()
          )
        );
      }
    });

    console.log("✅ News Fetched:", newsArticles);
    return newsArticles;
  } catch (error) {
    console.error("❌ News Fetching Error:", error);
    return [];
  }
};


// Login User
export const loginUser = async () => {
  try {
    const response = await axios.get(`${API_URL}/login`);
    return response.data;
  } catch (error) {
    return handleApiError(error);
  }
};

// Send Message to Chatbot
export const sendMessageToChatbot = async (message) => {
  try {
    const response = await axios.post(`${API_URL}/chat`, { message });
    return response.data;
  } catch (error) {
    return handleApiError(error);
  }
};

// Create a new chat thread

// ✅ Create chat thread
export const createThread = async () => {
  try {
    const response = await axios.post(
      `${API_URL}/api/assistant/thread/create`, 
      {}, // ✅ Send an empty object if no body is required
      {
        headers: {
          "Content-Type": "application/json", // ✅ Ensure correct content type
        },
      }
    );
    return response.data.threadId; // ✅ Extract threadId
  } catch (error) {
    console.error("❌ Error creating chat thread:", error);
    throw error;
  }
};

// ✅ Send message to assistant thread
export async function* sendMessageToThread({ threadId, assistantId, temperature, message }) {
  try {
    const response = await fetch(`${API_URL}/api/assistant/thread/chat`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ threadId, assistantId, temperature, message }),
    });

    if (!response.body) {
      throw new Error("No response body");
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let finalText = "";
    let buffer = ""; // ✅ Buffer to accumulate partial JSON

    while (true) {
      const { value, done } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value, { stream: true });

      // ✅ Accumulate chunk data safely
      buffer += chunk;

      // ✅ Process each complete "data: " line separately
      const lines = buffer.split("\n");

      for (let i = 0; i < lines.length; i++) {
        let line = lines[i].trim();

        if (line.startsWith("data: ")) {
          try {
            let jsonString = line.replace("data: ", "").trim();

            // ✅ Ignore empty or malformed JSON objects
            if (!jsonString || jsonString === "[DONE]") continue;

            // ✅ Parse safely, ignoring trailing commas or incomplete data
            const parsedData = JSON.parse(jsonString);

            // ✅ Only process valid assistant responses (delta updates)
            if (parsedData.object === "thread.message.delta") {
              const newText = parsedData.delta?.content?.[0]?.text?.value || "";
              finalText += newText;

              // ✅ Yield the latest text update for real-time UI rendering
              yield finalText;
            }
          } catch (error) {
            console.warn("⚠️ Skipping invalid JSON chunk:", line);
          }
        }
      }

      // ✅ Keep only the last part of buffer (incomplete JSON fragments)
      buffer = lines[lines.length - 1];
    }
  } catch (error) {
    console.error("❌ Error receiving streaming response:", error);
    yield "⚠️ AI response failed.";
  }
}
export const fetchUsers = async () => {
  try {
    const response = await axios.get(`${API_URL}/api/user`);
    return response.data; // Assumes an array of user objects: [{ id, name }, ...]
  } catch (error) {
    return handleApiError(error);
  }
};


export const fetchVectorStores = async () => {
  try {
    const response = await axios.get(`${API_URL}/api/vectorstore`);
    return response.data.map((vs) => new VectorStore(vs));
  } catch (error) {
    return handleApiError(error);
  }
};

// New functions for enhanced functionality
export const createVectorStore = async (name, files) => {
  try {
    const formData = new FormData();
    formData.append('name', name);
    files.forEach(file => formData.append('files', file));

    const response = await axios.post(`${API_URL}/api/vectorstore/create`, formData, {
      headers: { 
        'Content-Type': 'multipart/form-data',
        'Accept': 'application/json'
      }
    });
    return new VectorStore(response.data);
  } catch (error) {
    return handleApiError(error);
  }
};

export const addDocumentsToVectorStore = async (vectorStoreId, files) => {
  try {
    const formData = new FormData();
    files.forEach(file => formData.append('files', file));

    const response = await axios.post(
      `${API_URL}/api/vectorstore/${vectorStoreId}/documents`,
      formData,
      { 
        headers: { 
          'Content-Type': 'multipart/form-data',
          'Accept': 'application/json'
        } 
      }
    );
    return response.data;
  } catch (error) {
    return handleApiError(error);
  }
};

export const queryVectorStore = async (vectorStoreId, query) => {
  try {
    const response = await axios.post(
      `${API_URL}/api/vectorstore/${vectorStoreId}/query`,
      { query },
      { headers: { 'Content-Type': 'application/json' } }
    );
    return response.data;
  } catch (error) {
    return handleApiError(error);
  }
};


export const callOrchestrator = async (messageInput, sessionId = "default") => {
  try {
    const response = await axios.post(
      "http://localhost:5005/chat",
      { userQuery: messageInput },
      {
        headers: {
          "x-session-id": sessionId, // Pass sessionId for memory
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error("❌ LangChain Orchestrator Error:", error);
    throw error;
  }
};











import React, { useState, useContext, useEffect } from "react";
import { Avatar, IconButton, Button, Dropdown } from "rsuite";
import {
  Home,
  AppWindow,
  Plus,
  History,
  MoreVertical,
  ChevronDown,
  ChevronUp
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import GearIcon from "@rsuite/icons/Gear";
import SettingsView from "../Pages/Forms/Settings";
import { AssistantContext } from "../context/AssistantContext";

export default function Sidebar() {
  const navigate = useNavigate();
  const { selectedAssistant } = useContext(AssistantContext);
  const [chatHistory, setChatHistory] = useState([]);
  const [isChatHistoryOpen, setIsChatHistoryOpen] = useState(false);
  const [hoveredChatIndex, setHoveredChatIndex] = useState(null);

  // Listen for the custom "chatHistoryUpdated" event to update chat history immediately.
  useEffect(() => {
    const fetchChatHistory = () => {
      const savedChats = JSON.parse(localStorage.getItem("chatHistory")) || [];
      setChatHistory(savedChats);
    };
    fetchChatHistory();
    window.addEventListener("chatHistoryUpdated", fetchChatHistory);
    return () => window.removeEventListener("chatHistoryUpdated", fetchChatHistory);
  }, []);

  // Improved cookie retrieval function.
  const getCookie = (name) => {
    const cookies = document.cookie.split("; ");
    for (let cookie of cookies) {
      const [key, value] = cookie.split("=");
      if (key === name) return decodeURIComponent(value);
    }
    return null;
  };

  const userName = getCookie("user_id") || "Guest";
  const [showDrawer, setShowDrawer] = useState(false);

  // Logout handler.
  const handleLogout = () => {
    ["user_id", "access_token", "id_token", "assistants"].forEach((cookieName) => {
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    });
    localStorage.removeItem("chatHistory");
    navigate("/login");
  };

  // Load a saved chat session into ChatPage only when the history link is clicked.
  const loadChatSession = (chatIndex) => {
    const chatData = chatHistory[chatIndex];
    if (chatData) {
      navigate("/chat", {
        state: {
          loadHistory: true,
          chatSession: chatData.messages,
          assistant: selectedAssistant
        }
      });
    }
  };

  // Delete a chat history item.
  const deleteChatSession = (index) => {
    const updatedChats = chatHistory.filter((_, i) => i !== index);
    setChatHistory(updatedChats);
    localStorage.setItem("chatHistory", JSON.stringify(updatedChats));
  };

  // Helper to limit preview text to first 3 words.
  const getPreviewText = (text) => {
    if (!text) return "";
    const words = text.trim().split(/\s+/);
    const truncated = words.slice(0, 3).join(" ");
    return words.length > 3 ? truncated + "..." : truncated;
  };

  return (
    <div className="sidebar">
      {/* User Profile Section */}
      <div className="sidebar-profile">
        <Avatar
          src="https://randomuser.me/api/portraits/men/81.jpg"
          style={{ width: "30px", height: "30px", borderRadius: "50%" }}
        />
        <div className="profile-info">
          <span className="sidebar-profile-name">{userName}</span>
          <IconButton
            onClick={() => setShowDrawer(true)}
            appearance="subtle"
            className="settings-btn"
            icon={<GearIcon style={{ fontSize: 20 }} />}
          />
        </div>
      </div>

      <h5 className="sidebar-heading">Enterprise Chatbot</h5>

      {/* Navigation Links */}
      <nav className="sidebar-nav">
        <div className="nav-item" onClick={() => navigate("/")}>
          <Home size={18} /> Home
        </div>

        {/* Chat Section (Expandable) */}
        <div className="nav-item" onClick={() => setIsChatHistoryOpen(!isChatHistoryOpen)}>
          <AppWindow size={18} /> Chat
          {isChatHistoryOpen ? (
            <ChevronUp size={16} className="chevron-icon" />
          ) : (
            <ChevronDown size={16} className="chevron-icon" />
          )}
        </div>

        {/* Chat History */}
        {isChatHistoryOpen && chatHistory.length > 0 && (
          <div style={{ marginLeft: "10px" }}>
            {chatHistory.map((chat, index) => (
              <div
                key={index}
                className="nav-item" // Reuse nav-item style for consistent hover effect
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  padding: "8px",
                  cursor: "pointer"
                }}
                onMouseEnter={() => setHoveredChatIndex(index)}
                onMouseLeave={() => setHoveredChatIndex(null)}
                onClick={() => loadChatSession(index)}
              >
                {/* Left side: History icon + truncated text */}
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    flex: 1,
                    overflow: "hidden"
                  }}
                >
                  <History size={16} style={{ marginRight: "8px" }} />
                  <span
                    style={{
                      whiteSpace: "nowrap",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      fontSize: "0.9rem"
                    }}
                  >
                    {getPreviewText(chat?.messages?.[0]?.text)}
                  </span>
                </div>

                {/* Right side: Three-dot dropdown (only on hover) */}
                {hoveredChatIndex === index && (
                  <div onClick={(e) => e.stopPropagation()}>
                    <Dropdown
                      placement="leftStart"
                      noCaret
                      renderTitle={() => (
                        <MoreVertical size={16} style={{ marginLeft: "8px", color: "#ccc" }} />
                      )}
                      className="chat-history-dropdown"
                    >
                      <Dropdown.Item onClick={() => deleteChatSession(index)}>🗑️ Delete</Dropdown.Item>
                      <Dropdown.Item>🔗 Share</Dropdown.Item>
                    </Dropdown>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        <div className="nav-item" onClick={() => navigate("/graph")}>
          <AppWindow size={18} /> Knowledge Graph
        </div>
        <div className="nav-item" onClick={() => navigate("/agentUI")}>
          <agentUI size={18} /> Agent UI
        </div>
      </nav>

      {/* Start Chat & Logout (fixed at bottom) */}
      <div className="sidebar-footer">
        <Button
          appearance="primary"
          block
          onClick={() => navigate("/chat", { state: { newChat: true } })}
          className="start-chat-btn"
        >
          <Plus size={18} className="mr-2" /> Start New Chat
        </Button>
        <Button appearance="subtle" block onClick={handleLogout} className="logout-btn">
          Logout
        </Button>
      </div>

      {/* Settings Drawer */}
      <SettingsView open={showDrawer} onClose={() => setShowDrawer(false)} assistant={selectedAssistant} />
    </div>
  );
}

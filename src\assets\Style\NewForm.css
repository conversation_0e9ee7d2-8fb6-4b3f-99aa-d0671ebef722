@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap');

.perplexity-card {
  width: 1000px;
  max-width: 95%;
  background: #ffffff;
  border-radius: 10px;
  padding: 25px;
  border: 1px solid #ececec;
  margin: 30px auto 0;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.05);
  font-family: 'Inter', sans-serif;
  color: #2f3e46;
}

/* Tabs Header */
.tabs-header {
  display: flex;
  border-bottom: 1px solid #ddd;
  margin-bottom: 24px;
}

.tab-button {
  flex: 1;
  padding: 12px;
  font-size: 18px;
  font-weight: 600;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.tab-button.active {
  border-bottom-color: #2f3e46;
}

/* Section Heading */
.section-heading {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 20px;
  color: #2f3e46;
}

/* Settings Item */
.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 15px 0;
  border-top: 1px solid #eaeaea;
}

.settings-item:first-child {
  border-top: none;
}

/* Left side */
.item-left {
  max-width: 40%;
}

.item-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 5px;
}

.item-subtitle {
  font-size: 16px;
  color: #64748b;
  line-height: 1.5;
}

/* Right side */
.item-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.item-right .form-group {
  margin-left: auto;
  text-align: right;
}

/* Form Controls */
.form-group {
  width: 100%;
  float: right;
}

.form-control {
  width: 100%;
  padding: 12px 16px;
  font-size: 18px;
  border: 1px solid #ccc;
  border-radius: 6px;
  box-sizing: border-box;
  float: right;
}

.textarea-control {
  resize: vertical;
  min-height: 120px;
}

.select-control {
  padding: 12px 16px;
  font-size: 18px;
  border: 1px solid #ccc;
  border-radius: 6px;
}

/* Slider control */
.slider-control {
  width: 250px;
  height: 25px;
}

/* Temperature value display */
.temp-value {
  font-size: 18px;
  margin-left: 10px;
}

/* Checkbox styling */
.checkbox-control {
  transform: scale(1.5);
  cursor: pointer;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .perplexity-card {
    width: 90%;
  }
  .settings-item {
    flex-direction: column;
    align-items: flex-start;
  }
  .item-right {
    justify-content: flex-start;
    margin-top: 10px;
  }
}
.save-btn {
  display: flex !important;
  align-items: center;
  justify-content: center;
  background: #2563eb !important;
  color: white !important;
  padding: 10px 18px !important;
  border-radius: 6px !important;
  font-weight: bold !important;
  border: none !important;
  cursor: pointer !important;
  transition: background 0.2s ease;
  z-index: 9999;
  height: 40px;
  min-width: 160px;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1); /* Adds subtle depth */
  margin-right: 40px;
}

.save-btn:hover {
  background: #1e40af !important;
}
.form-group {
  margin-bottom: 20px;
  font-family: 'Inter', sans-serif;
}

.form-label {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #2f3e46;
  margin-bottom: 6px;
}

.perplexity-input {
  width: 250px; /* or adjust as needed */
  padding: 12px 16px;
  font-size: 18px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  outline: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.perplexity-input:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
}

.form-subtitle {
  font-size: 16px;
  color: #64748b;
  margin-top: 4px;
  max-width: 90%;
}
.custom-tabs .rs-nav-item {
  font-weight: 700;
  font-size: 20px;
}

/* Custom style for RSuite Checkbox group */
.user-checkboxes .rs-checkbox-wrapper {
  display: inline-flex;
  align-items: center;
  gap: 8px; /* Adjust this value as needed */
  margin-bottom: 4px;
}

/* Optionally, adjust the label margin if necessary */
.user-checkboxes .rs-checkbox-label {
  margin-left: 8px; /* Reduce the left margin */
}

/* ✅ Settings Page - Two Card Layout */
.settings-container {
  display: flex;
  gap: 20px;
  margin-top: 30px;
}

/* ✅ Left Card (Navigation Tabs) */
.settings-tabs-card {
  width: 250px;
  background: #f9fafb;
  border-radius: 10px;
  padding: 15px;
  border: 1px solid #e5e7eb;
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.1);
}

/* ✅ Right Card (Main Content) */
.settings-content-card {
  flex: 1;
  background: white;
  border-radius: 10px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.1);
}

  /* Page Title */
  .page-title {
    font-size: 36px !important;
    font-weight: bold !important;
    color: #6c757d !important;
    margin: 0 !important;
  }
  /* Tab labels */
  .custom-tabs .rs-tabs-tab {
    font-size: 18px !important;
    font-weight: bold !important;
    color: #6c757d !important;
  }
  /* Section Headings */
  .section-heading {
    font-size: 24px !important;
    font-weight: 700 !important;
    color: #6c757d !important;
    margin-bottom: 16px !important;
  }
  /* Field Labels */
  .settings-label {
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #6c757d !important;
    margin-bottom: 4px !important;
    display: inline-block !important;
  }
  /* Scrollable container for AntTree */
  .scrollable-container {
    height: 300px !important;
    overflow-y: auto !important;
  }
  /* Selected nodes in Permissions Tree */
  .user-permissions-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
    background-color: #cce5ff !important;
    color: #004085 !important;
    font-weight: bold !important;
  }
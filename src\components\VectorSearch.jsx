import React, { useState } from 'react';
import { VectorStoreService } from '../services/vectorStore';
import { Input, Button, List, message } from 'antd';

const VectorSearch = () => {
    const [query, setQuery] = useState('');
    const [results, setResults] = useState([]);
    const [loading, setLoading] = useState(false);

    const handleSearch = async () => {
        if (!query.trim()) {
            message.warning('Please enter a search query');
            return;
        }

        setLoading(true);
        try {
            const searchResults = await VectorStoreService.searchVectors(query);
            setResults(searchResults);
        } catch (error) {
            message.error('Error performing search');
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="p-4">
            <div className="flex gap-2 mb-4">
                <Input
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    placeholder="Enter search query"
                    onPressEnter={handleSearch}
                />
                <Button 
                    type="primary"
                    onClick={handleSearch}
                    loading={loading}
                >
                    Search
                </Button>
            </div>

            <List
                dataSource={results}
                renderItem={(item) => (
                    <List.Item>
                        <div>
                            <div>{item.document.text}</div>
                            <div className="text-sm text-gray-500">
                                Similarity: {(item.similarity * 100).toFixed(2)}%
                            </div>
                        </div>
                    </List.Item>
                )}
            />
        </div>
    );
};

export default VectorSearch;
/* AirlineTicket Component Styles */
.premium-airline-ticket {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  perspective: 1000px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.ticket-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

.ticket-front {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Airline Header */
.airline-header {
  background: #d71921; /* Virgin Atlantic red */
  padding: 15px;
  color: white;
  position: relative;
  overflow: hidden;
}

.airline-logo {
  font-weight: 700;
  font-size: 18px;
  letter-spacing: 1px;
  position: relative;
  z-index: 2;
}

.airline-pattern {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-image: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.1) 50%, rgba(255,255,255,0.1) 75%, transparent 75%, transparent);
  background-size: 10px 10px;
  opacity: 0.2;
}

/* Cities and Flight Path */
.ticket-cities {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: #f8f9fa;
}

.departure, .arrival {
  text-align: center;
  flex: 1;
}

.city-code {
  font-size: 28px;
  font-weight: 700;
  color: #333;
}

.city-name {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.time {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.date {
  font-size: 12px;
  color: #666;
}

.flight-path {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 15px;
}

.plane-icon {
  font-size: 18px;
  color: #d71921;
  margin-bottom: 5px;
}

.path-line {
  width: 100px;
  height: 2px;
  background-color: #d71921;
  position: relative;
}

.flight-info {
  text-align: center;
  margin-top: 5px;
}

.flight-number {
  font-size: 12px;
  font-weight: 600;
  color: #333;
}

.flight-duration {
  font-size: 11px;
  color: #666;
}

/* Ticket Details */
.ticket-details {
  padding: 15px 20px;
  border-top: 1px dashed #ddd;
  border-bottom: 1px dashed #ddd;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-item {
  flex: 1;
}

.detail-label {
  font-size: 11px;
  color: #666;
  margin-bottom: 2px;
}

.detail-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.status {
  color: #16a34a; /* Green for confirmed status */
}

/* Ticket Footer */
.ticket-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
}

.pnr-label {
  font-size: 11px;
  color: #666;
}

.pnr-value {
  font-size: 16px;
  font-weight: 700;
  letter-spacing: 1px;
  color: #333;
}

.miles-value {
  font-size: 12px;
  font-weight: 600;
  color: #d71921;
  background-color: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
}

/* Modal Ticket */
.modal-ticket {
  max-width: 100%;
  margin-bottom: 20px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .premium-ticket-container {
    padding: 10px 0;
  }
  
  .airport-code {
    font-size: 24px;
  }
  
  .flight-icon {
    font-size: 18px;
  }
  
  .detail-value {
    font-size: 14px;
  }
}

/* Animation for ticket appearance */
@keyframes ticketAppear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.premium-ticket-container {
  animation: ticketAppear 0.5s ease-out;
}

/* Premium Flight Card */
.flight-card {
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 16px;
  border-left: 4px solid #d71921;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* Flight Timeline */
.flight-timeline {
  display: flex;
  align-items: center;
  margin: 24px 0;
  position: relative;
}

.timeline-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #d71921;
  position: relative;
  z-index: 1;
}

.timeline-line {
  flex: 1;
  height: 2px;
  background: #e2e8f0;
  position: relative;
}

.timeline-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: #d71921;
}

/* Enhanced Status Badges */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Formatted Response Styling */
.formatted-response {
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

.section {
  margin-bottom: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.section-emoji {
  font-size: 20px;
  margin-right: 10px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #d71921;
}

.section-content {
  padding: 15px;
  line-height: 1.6;
}

.content-line {
  margin-bottom: 8px;
}

.content-line:last-child {
  margin-bottom: 0;
}

.bold {
  font-weight: 600;
}

/* Virgin Atlantic Ticket Styles */
.virgin-atlantic-ticket {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.va-ticket-container {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Header with Virgin Atlantic branding */
.va-ticket-header {
  background-color: #e31837; /* Virgin Atlantic red */
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  position: relative;
}

.va-ticket-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 5px;
  background-image: linear-gradient(45deg, rgba(255,255,255,0.2) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.2) 75%, transparent 75%, transparent);
  background-size: 10px 10px;
}

.va-logo {
  display: flex;
  align-items: center;
}

.va-logo-icon {
  font-size: 18px;
  margin-right: 8px;
}

.va-logo-text {
  font-weight: 700;
  font-size: 16px;
  letter-spacing: 1px;
}

.va-ticket-type {
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 1px;
  background-color: rgba(255,255,255,0.2);
  padding: 4px 8px;
  border-radius: 4px;
}

/* Flight Information */
.va-flight-info {
  display: flex;
  justify-content: space-between;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.va-flight-number, .va-flight-date, .va-flight-class {
  display: flex;
  flex-direction: column;
}

.va-label {
  font-size: 10px;
  color: #6c757d;
  margin-bottom: 2px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.va-value {
  font-size: 14px;
  font-weight: 600;
  color: #212529;
}

/* Route Information */
.va-route-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: white;
}

.va-departure, .va-arrival {
  text-align: center;
  flex: 1;
}

.va-airport-code {
  font-size: 32px;
  font-weight: 700;
  color: #212529;
  margin-bottom: 5px;
}

.va-airport-name {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 8px;
}

.va-time {
  font-size: 18px;
  font-weight: 600;
  color: #212529;
}

.va-flight-path {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 15px;
  flex: 1;
}

.va-duration {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 5px;
}

.va-path-line {
  width: 100%;
  height: 2px;
  background-color: #e9ecef;
  position: relative;
  margin: 10px 0;
}

.va-plane-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 16px;
  color: #e31837;
  background-color: white;
  padding: 0 5px;
}

.va-status {
  font-size: 12px;
  font-weight: 600;
  color: #198754; /* Bootstrap green */
  margin-top: 5px;
}

/* Passenger Information */
.va-passenger-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  padding: 20px;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  border-bottom: 1px solid #e9ecef;
}

.va-passenger-name, .va-passenger-seat, .va-passenger-gate, .va-passenger-boarding {
  display: flex;
  flex-direction: column;
}

/* Footer with PNR and Barcode */
.va-ticket-footer {
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.va-pnr {
  display: flex;
  flex-direction: column;
}

.va-barcode {
  width: 150px;
  height: 40px;
  background-image: linear-gradient(90deg, #000 0%, #000 10%, transparent 10%, transparent 15%, #000 15%, #000 20%, transparent 20%, transparent 40%, #000 40%, #000 45%, transparent 45%, transparent 55%, #000 55%, #000 60%, transparent 60%, transparent 70%, #000 70%, #000 80%, transparent 80%, transparent 90%, #000 90%, #000 100%);
  background-size: 100% 100%;
  border-radius: 4px;
}

/* Modal Ticket */
.modal-ticket {
  max-width: 100%;
  margin-bottom: 20px;
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .va-route-info {
    flex-direction: column;
    gap: 20px;
  }
  
  .va-flight-path {
    width: 100%;
    flex-direction: row;
    justify-content: space-between;
  }
  
  .va-path-line {
    width: 60%;
  }
  
  .va-passenger-info {
    grid-template-columns: 1fr;
  }
}

/* Compact Virgin Atlantic Ticket Styles */
.va-compact-ticket {
  width: 100%;
  max-width: 450px;
  margin: 0 auto;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.va-compact-container {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 3px 10px rgba(0,0,0,0.08);
  border: 1px solid #e9ecef;
}

/* Header with Logo and Flight Info */
.va-compact-header {
  background-color: #e31837; /* Virgin Atlantic red */
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.va-compact-logo {
  display: flex;
  align-items: center;
}

.va-logo-icon {
  font-size: 16px;
  margin-right: 6px;
}

.va-logo-text {
  font-weight: 700;
  font-size: 14px;
  letter-spacing: 0.5px;
}

.va-compact-flight {
  text-align: right;
}

.va-compact-flight-number {
  font-weight: 600;
  font-size: 14px;
}

.va-compact-flight-class {
  font-size: 11px;
  opacity: 0.9;
}

/* Route Information */
.va-compact-route {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.va-compact-departure, .va-compact-arrival {
  text-align: center;
  flex: 1;
}

.va-compact-airport-code {
  font-size: 24px;
  font-weight: 700;
  color: #212529;
  line-height: 1;
}

.va-compact-airport-name {
  font-size: 10px;
  color: #6c757d;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
}

.va-compact-time {
  font-size: 14px;
  font-weight: 600;
  color: #212529;
}

.va-compact-date {
  font-size: 10px;
  color: #6c757d;
}

.va-compact-flight-path {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 10px;
  flex: 0.8;
}

.va-compact-duration {
  font-size: 10px;
  color: #6c757d;
  margin-bottom: 3px;
}

.va-compact-path-line {
  width: 100%;
  height: 2px;
  background-color: #e9ecef;
  position: relative;
}

.va-compact-plane-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  color: #e31837;
  background-color: #f8f9fa;
  padding: 0 3px;
}

/* Passenger Details */
.va-compact-details {
  padding: 10px 15px;
  border-bottom: 1px solid #e9ecef;
}

.va-compact-detail-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 6px;
}

.va-compact-detail-row {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.va-compact-detail-row .va-compact-detail-item {
  flex: 1;
  margin-bottom: 0;
}

.va-compact-label {
  font-size: 9px;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.va-compact-value {
  font-size: 12px;
  font-weight: 600;
  color: #212529;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Footer with PNR and Status */
.va-compact-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f8f9fa;
}

.va-compact-pnr {
  display: flex;
  flex-direction: column;
}

.va-compact-pnr-value {
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1px;
  color: #212529;
}

.va-compact-status {
  font-size: 11px;
  font-weight: 600;
  color: #198754; /* Bootstrap green */
  background-color: rgba(25, 135, 84, 0.1);
  padding: 3px 8px;
  border-radius: 4px;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .va-compact-ticket {
    max-width: 100%;
  }
  
  .va-compact-airport-name {
    max-width: 80px;
  }
}



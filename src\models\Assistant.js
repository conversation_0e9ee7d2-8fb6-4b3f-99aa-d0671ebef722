export class Assistant {
    constructor({ id, assistantId, name, instructions, model, temperature, providerId, current, default: isDefault, userIds, vectorStoreIds }) {
      this.id = id;
      this.assistantId = assistantId;
      this.name = name;
      this.instructions = instructions;
      this.model = model;
      this.temperature = temperature;
      this.providerId = providerId;
      this.current = current;
      this.isDefault = isDefault;
      this.userIds = userIds;
      this.vectorStoreIds = vectorStoreIds;
    }
  
    // ✅ Short Description (First 25 Words)
    getShortDescription(wordLimit = 25) {
      return this.instructions.split(" ").slice(0, wordLimit).join(" ") + "...";
    }
  
    // ✅ Format Assistant Type Based on Model
    getAssistantType() {
      return this.model.includes("gpt") ? "AI-powered Assistant" : "Standard Assistant";
    }
  
    // ✅ Check if Assistant is Default
    isDefaultAssistant() {
      return this.isDefault ? "✅ Default Assistant" : "🚀 Custom Assistant";
    }
  }
  
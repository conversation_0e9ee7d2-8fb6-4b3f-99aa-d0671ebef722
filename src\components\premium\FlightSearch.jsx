import React, { useState } from 'react';
import { 
  Form, Button, ButtonGroup, InputGroup, 
  DatePicker, InputNumber, SelectPicker, 
  Panel, Loader, Toggle, Radio, RadioGroup
} from 'rsuite';
import { Search, ArrowRight, ArrowLeft, Calendar, Users, Briefcase, Map } from 'react-feather';
import './PremiumStyles.css';

// Sample data for dropdowns
const airports = [
  { label: 'London Heathrow (LHR)', value: 'LHR' },
  { label: 'New York JFK (JFK)', value: 'JFK' },
  { label: 'Los Angeles (LAX)', value: 'LAX' },
  { label: '<PERSON> Charles <PERSON> (CDG)', value: 'CDG' },
  { label: 'Dubai International (DXB)', value: 'DXB' },
  { label: 'Singapore Changi (SIN)', value: 'SIN' },
  { label: 'Tokyo Narita (NRT)', value: 'NRT' },
  { label: 'Sydney (SYD)', value: 'SYD' },
  { label: 'Hong Kong (HKG)', value: 'HKG' },
  { label: 'Amsterdam Schiphol (AMS)', value: 'AMS' }
];

const cabinClasses = [
  { label: 'Economy', value: 'economy' },
  { label: 'Premium Economy', value: 'premium_economy' },
  { label: 'Business', value: 'business' },
  { label: 'First Class', value: 'first' }
];

const FlightSearch = ({ onSearch, loading, className }) => {
  const [tripType, setTripType] = useState('round');
  const [origin, setOrigin] = useState('');
  const [destination, setDestination] = useState('');
  const [departDate, setDepartDate] = useState(null);
  const [returnDate, setReturnDate] = useState(null);
  const [passengers, setPassengers] = useState(1);
  const [cabinClass, setCabinClass] = useState('economy');
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [directFlights, setDirectFlights] = useState(false);
  const [flexibleDates, setFlexibleDates] = useState(false);

  // Swap origin and destination
  const handleSwap = () => {
    const temp = origin;
    setOrigin(destination);
    setDestination(temp);
  };

  // Handle search submission
  const handleSubmit = () => {
    if (!origin || !destination || !departDate) {
      // Show validation error
      return;
    }

    const searchParams = {
      tripType,
      origin,
      destination,
      departDate,
      returnDate: tripType === 'round' ? returnDate : null,
      passengers,
      cabinClass,
      directFlights,
      flexibleDates
    };

    onSearch && onSearch(searchParams);
  };

  // Custom field component for consistent styling
  const FormField = ({ label, icon, children }) => (
    <div className="premium-form-field">
      <div className="field-label">
        {icon}
        <span>{label}</span>
      </div>
      <div className="field-input">
        {children}
      </div>
    </div>
  );

  return (
    <div className={`premium-flight-search ${className || ''}`}>
      <Panel bordered shaded>
        <h3 className="search-title">Find Your Perfect Flight</h3>
        
        <div className="trip-type-selector">
          <ButtonGroup justified>
            <Button 
              appearance={tripType === 'round' ? 'primary' : 'ghost'}
              onClick={() => setTripType('round')}
            >
              Round Trip
            </Button>
            <Button 
              appearance={tripType === 'one-way' ? 'primary' : 'ghost'}
              onClick={() => setTripType('one-way')}
            >
              One Way
            </Button>
            <Button 
              appearance={tripType === 'multi-city' ? 'primary' : 'ghost'}
              onClick={() => setTripType('multi-city')}
            >
              Multi-City
            </Button>
          </ButtonGroup>
        </div>
        
        <Form fluid className="search-form">
          <div className="form-row">
            <FormField label="From" icon={<Map size={16} />}>
              <SelectPicker 
                data={airports}
                value={origin}
                onChange={setOrigin}
                placeholder="Select origin"
                block
                cleanable={false}
              />
            </FormField>
            
            <div className="swap-button-container">
              <Button 
                appearance="subtle" 
                className="swap-button"
                onClick={handleSwap}
              >
                <ArrowRight className="swap-icon right" />
                <ArrowLeft className="swap-icon left" />
              </Button>
            </div>
            
            <FormField label="To" icon={<Map size={16} />}>
              <SelectPicker 
                data={airports}
                value={destination}
                onChange={setDestination}
                placeholder="Select destination"
                block
                cleanable={false}
              />
            </FormField>
          </div>
          
          <div className="form-row">
            <FormField label="Depart" icon={<Calendar size={16} />}>
              <DatePicker 
                value={departDate}
                onChange={setDepartDate}
                placeholder="Select date"
                block
                format="DD MMM YYYY"
                ranges={[
                  {
                    label: 'Today',
                    value: new Date()
                  },
                  {
                    label: 'Tomorrow',
                    value: new Date(Date.now() + 86400000)
                  }
                ]}
              />
            </FormField>
            
            {tripType === 'round' && (
              <FormField label="Return" icon={<Calendar size={16} />}>
                <DatePicker 
                  value={returnDate}
                  onChange={setReturnDate}
                  placeholder="Select date"
                  block
                  format="DD MMM YYYY"
                  disabled={!departDate}
                  disabledDate={date => date < departDate}
                />
              </FormField>
            )}
          </div>
          
          <div className="form-row">
            <FormField label="Passengers" icon={<Users size={16} />}>
              <InputNumber 
                value={passengers}
                onChange={setPassengers}
                min={1}
                max={9}
                block
              />
            </FormField>
            
            <FormField label="Cabin Class" icon={<Briefcase size={16} />}>
              <SelectPicker 
                data={cabinClasses}
                value={cabinClass}
                onChange={setCabinClass}
                placeholder="Select class"
                block
                cleanable={false}
              />
            </FormField>
          </div>
          
          <div className="advanced-options">
            <Button 
              appearance="link" 
              onClick={() => setShowAdvanced(!showAdvanced)}
            >
              {showAdvanced ? 'Hide' : 'Show'} Advanced Options
            </Button>
            
            {showAdvanced && (
              <div className="advanced-options-content">
                <div className="option-row">
                  <div className="option-item">
                    <span>Direct Flights Only</span>
                    <Toggle 
                      checked={directFlights}
                      onChange={setDirectFlights}
                    />
                  </div>
                  
                  <div className="option-item">
                    <span>Flexible Dates (±3 days)</span>
                    <Toggle 
                      checked={flexibleDates}
                      onChange={setFlexibleDates}
                    />
                  </div>
                </div>
                
                <div className="option-row">
                  <RadioGroup inline name="preferredAirline" defaultValue="any">
                    <Radio value="any">Any Airline</Radio>
                    <Radio value="virgin">Virgin Atlantic Only</Radio>
                    <Radio value="alliance">SkyTeam Alliance</Radio>
                  </RadioGroup>
                </div>
              </div>
            )}
          </div>
          
          <div className="search-button-container">
            <Button 
              appearance="primary" 
              block 
              size="lg"
              onClick={handleSubmit}
              disabled={loading}
            >
              {loading ? (
                <Loader content="Searching..." />
              ) : (
                <>
                  <Search size={18} />
                  <span>Search Flights</span>
                </>
              )}
            </Button>
          </div>
        </Form>
      </Panel>
    </div>
  );
};

export default FlightSearch;

// DirectGraph.js
import React, { useEffect, useRef } from 'react';
import cytoscape from 'cytoscape';
import nodeHtmlLabel from 'cytoscape-node-html-label';

// Register the HTML label extension with Cytoscape
nodeHtmlLabel(cytoscape);

const DirectGraph = () => {
  const cyRef = useRef(null);

  useEffect(() => {
    const cyInstance = cytoscape({
      container: cyRef.current,
      elements: [
        // Define nodes with extra data properties
        { data: { id: 'node1', label: 'Node 1', info: 'Detailed info about Node 1' } },
        { data: { id: 'node2', label: 'Node 2', info: 'Detailed info about Node 2' } },
        { data: { id: 'node3', label: 'Node 3', info: 'Detailed info about Node 3' } },
        { data: { id: 'node4', label: 'Node 4', info: 'Detailed info about Node 4' } },
        // Define edges connecting nodes
        { data: { source: 'node1', target: 'node2' } },
        { data: { source: 'node1', target: 'node3' } },
        { data: { source: 'node2', target: 'node4' } },
        { data: { source: 'node3', target: 'node4' } }
      ],
      layout: { name: 'cose' },
      style: [
        // Remove default node background so our HTML label is visible
        {
          selector: 'node',
          style: {
            'background-opacity': 0, // Hide the default circular node
            'label': '' // Remove any default label text
          }
        },
        {
          selector: 'edge',
          style: {
            'width': 2,
            'line-color': '#ccc'
          }
        }
      ]
    });

    // Initialize the node HTML labels
    cyInstance.nodeHtmlLabel([
      {
        // Apply to every node
        query: 'node',
        tpl: function (data) {
          // Return an HTML card for each node
          return `
            <div class="node-card">
              <h4>${data.label}</h4>
              <p>${data.info}</p>
            </div>
          `;
        },
        cssClass: '', // Additional classes can be added here
        valign: 'center',
        halign: 'center'
      }
    ]);

    // Optional: add a click handler for nodes
    cyInstance.on('tap', 'node', (event) => {
      console.log('Tapped node:', event.target.data());
    });
  }, []);

  return (
    <div>
      {/* Container for the Cytoscape graph */}
      <div ref={cyRef} style={{ width: '800px', height: '600px' }} />
    </div>
  );
};

export default DirectGraph;

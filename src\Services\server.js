const express = require("express");
const axios = require("axios");
const jwt = require("jsonwebtoken");
const cookieParser = require("cookie-parser");

const app = express();
app.use(express.json());
app.use(cookieParser()); // ✅ Parse cookies

const CLIENT_ID = "1b79bfc5-b8df-4e7c-ae7c-0109cf80a577";
const CLIENT_SECRET = "****************************************";
const TENANT_ID = "6bdfd5a8-531f-4f14-9af1-dc7b444c3ad8";
const REDIRECT_URI = "http://localhost:3000/callback";

// ✅ Login endpoint (Redirect to Microsoft OAuth)
app.get("/login", (req, res) => {
  const authState = "random_state";
  const nonce = "random_nonce";

  const params = new URLSearchParams({
    response_type: "code id_token",
    client_id: CLIENT_ID,
    redirect_uri: REDIRECT_URI,
    state: authState,
    nonce: nonce,
    prompt: "select_account",
    scope: "openid profile email User.Read",
    response_mode: "form_post"
  });

  res.redirect(`https://login.microsoftonline.com/${TENANT_ID}/oauth2/v2.0/authorize?${params}`);
});

// ✅ Callback endpoint (Exchange code for token)
app.post("/callback", async (req, res) => {
  try {
    const { code } = req.body; // Get auth code from Microsoft

    // Exchange auth code for access token
    const tokenResponse = await axios.post(
      `https://login.microsoftonline.com/${TENANT_ID}/oauth2/v2.0/token`,
      new URLSearchParams({
        client_id: CLIENT_ID,
        client_secret: CLIENT_SECRET,
        code: code,
        redirect_uri: REDIRECT_URI,
        grant_type: "authorization_code"
      }),
      { headers: { "Content-Type": "application/x-www-form-urlencoded" } }
    );

    const accessToken = tokenResponse.data.access_token;
    const idToken = tokenResponse.data.id_token;

    // Decode ID token to get user info
    const decodedToken = jwt.decode(idToken);
    const userName = decodedToken.name || "Nitin Sinha"; // Fallback
    const userEmail = decodedToken.email || decodedToken.preferred_username;

    // ✅ Store user session in backend (no frontend cookies)
    req.session = { user: { name: userName, email: userEmail, accessToken } };

    // ✅ Send response to frontend (Only user data)
    res.json({ name: userName, email: userEmail });

  } catch (error) {
    console.error("OAuth Callback Error:", error);
    res.status(500).json({ error: "Authentication Failed" });
  }
});

// ✅ Endpoint for frontend to fetch user info
app.get("/user", (req, res) => {
  if (!req.session || !req.session.user) {
    return res.status(401).json({ error: "Not authenticated" });
  }
  res.json(req.session.user);
});

app.listen(3000, () => console.log("Server running on port 3000"));

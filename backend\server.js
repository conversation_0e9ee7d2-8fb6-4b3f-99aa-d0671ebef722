const express = require('express');
const cors = require('cors');
const vectorStoreRouter = require('./routes/vectorStore');
require('dotenv').config();

const app = express();

app.use(cors());
app.use(express.json());

// Vector store routes
app.use('/api/vectorstore', vectorStoreRouter);

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});


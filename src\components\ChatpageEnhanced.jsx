// import { QRCode } from 'qrcode.react'; // Uncomment if QR Code is needed
import { useState } from "react";
import { Dropdown } from "rsuite"; // Import Dropdown

const ChatpageEnhanced = ({ setMessages, sendQuickMessage }) => {
  const [language, setLanguage] = useState("English");
  const [flightStatus, setFlightStatus] = useState("On Time");
  const [isQRCodeVisible, setQRCodeVisible] = useState(false);

  const quickActions = [
    {
      title: "Help me book a flight",
      subtitle: "from San Francisco to London",
      prompt: "Help me book a flight from San Francisco to London",
    },
    {
      title: "What is the status",
      subtitle: "of flight BA142 flying tmrw?",
      prompt: "What is the status of flight BA142 flying tomorrow?",
    },
  ];

  const handleLanguageChange = (lang) => {
    setLanguage(lang);
    setMessages((prev) => [
      ...prev,
      { sender: "AI", text: `Language changed to ${lang}` },
    ]);
  };

  const handleFlightStatusRequest = () => {
    setMessages((prev) => [
      ...prev,
      { sender: "User", text: "What's the status of my flight?" },
      { sender: "AI", text: `Your flight BA142 is currently ${flightStatus}.` },
    ]);
  };

  const handleShowQRCode = () => {
    setQRCodeVisible(true);
    setMessages((prev) => [
      ...prev,
      { sender: "AI", text: "Here is your QR Boarding Pass:", isQRCode: true },
    ]);
  };

  const handleFlightDelay = () => {
    setFlightStatus("Delayed by 30 minutes");
    setMessages((prev) => [
      ...prev,
      { sender: "AI", text: "⚠️ Your flight BA142 has been delayed by 30 minutes." },
    ]);
  };

  return (
    <div className="quick-actions-container">
      {/* Quick Actions Dropdown */}
      <Dropdown title="Quick Actions" className="quick-actions-dropdown">
        <Dropdown.Item onClick={() => handleLanguageChange("Spanish")}>
          🌎 Change Language to Spanish
        </Dropdown.Item>
        <Dropdown.Item onClick={handleFlightStatusRequest}>
          ✈️ Check Flight Status
        </Dropdown.Item>
        <Dropdown.Item onClick={handleShowQRCode}>
          🎟️ Show QR Boarding Pass
        </Dropdown.Item>
        <Dropdown.Item onClick={handleFlightDelay}>
          ⏳ Flight Delay Alert
        </Dropdown.Item>
      </Dropdown>
    </div>
  );
};

export default ChatpageEnhanced;

/* Main container */
.chat-page-container {
  height: calc(100vh - 60px); /* Account for top header */
  width: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

/* Chat area */
.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  overflow: hidden;
  position: relative;
}

/* Messages container */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  margin-bottom: 20px;
}

/* Individual message styling */
.message {
  display: flex;
  margin-bottom: 20px;
  align-items: flex-start;
}

.user-message {
  justify-content: flex-end;
}

.ai-message {
  justify-content: flex-start;
}

/* Message bubbles */
.message-bubble {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-bubble {
  background: #d71921; /* Virgin red */
  color: white;
}

.ai-bubble {
  background: white;
  color: #333;
}

/* Avatar styling */
.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin: 0 10px;
}

/* Input area */
.input-area {
  position: sticky;
  bottom: 0;
  background: white;
  padding: 20px;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center;
  gap: 10px;
}

.message-input {
  flex: 1;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  resize: none;
  min-height: 24px;
  max-height: 120px;
  outline: none;
}

.message-input:focus {
  border-color: #d71921;
}

/* Action buttons */
.action-button {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  color: #666;
  transition: color 0.2s;
}

.action-button:hover {
  color: #d71921;
}

.send-button {
  background: #d71921;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.send-button:hover {
  background: #b5141b;
}

/* Loading indicator */
.typing-indicator {
  display: flex;
  gap: 4px;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 8px;
  width: fit-content;
}

.typing-dot {
  width: 6px;
  height: 6px;
  background: #d71921;
  border-radius: 50%;
  animation: bounce 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }
.typing-dot:nth-child(3) { animation-delay: 0s; }

@keyframes bounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

/* Scrollbar styling */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.messages-container::-webkit-scrollbar-thumb {
  background: #d71921;
  border-radius: 3px;
}

/* Message animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message {
  animation: fadeIn 0.3s ease-out;
}

/* Scroll to Bottom Button for ChatPage */
.chat-messages {
  position: relative;
}

.scroll-to-bottom-container {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 10;
  pointer-events: none;
}

.scroll-to-bottom-btn {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(215, 25, 33, 0.2);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  pointer-events: auto;
  color: #d71921;
  backdrop-filter: blur(8px);
  animation: slideUpFadeIn 0.3s ease-out;
}

.scroll-to-bottom-btn:hover {
  background: rgba(215, 25, 33, 0.1);
  border-color: rgba(215, 25, 33, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.scroll-to-bottom-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.scroll-to-bottom-btn svg {
  transition: transform 0.2s ease;
}

.scroll-to-bottom-btn:hover svg {
  transform: translateY(1px);
}

@keyframes slideUpFadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}


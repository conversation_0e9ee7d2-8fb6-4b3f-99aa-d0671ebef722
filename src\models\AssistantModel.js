// AssistantModel.js

/**
 * Creates an assistant model object.
 *
 * @param {Object} data - The assistant data.
 * @param {number|string} data.providerId - The provider ID.
 * @param {string} data.assistantId - A unique ID for the assistant.
 * @param {string} data.name - The name of the assistant.
 * @param {string} data.model - The model (e.g., GPT-4, GPT-3.5, Custom).
 * @param {number} data.temperature - Temperature value (0.0 - 1.0).
 * @param {number[]} data.userIds - Array of user IDs.
 * @param {string[]} data.vectorStoreIds - Array of vector store IDs.
 * @param {boolean} data.current - Whether this assistant is current.
 * @param {boolean} data.default - Whether this is the default assistant.
 * @param {string} data.instructions - Instructions for the assistant.
 *
 * @returns {Object} The assistant model object.
 */
 export function createAssistantModel({
    providerId = 0,
    assistantId = "",
    name = "",
    model = "",
    temperature = 0,
    userIds = [0],
    vectorStoreIds = ["string"],
    current = true,
    default: isDefault = true,
    instructions = "",
  }) {
    return {
      providerId: Number(providerId),
      assistantId,
      name,
      model,
      temperature: Number(temperature),
      userIds,
      vectorStoreIds,
      current,
      default: isDefault,
      instructions,
    };
  }
  
import axios from 'axios';

const API_URL = 'http://localhost:3000'; // Match your vite.config.js proxy setting

export class VectorStoreService {
    static async createVectorStore(documents) {
        try {
            const response = await axios.post(`${API_URL}/api/vectorstore`, {
                documents
            });
            return response.data;
        } catch (error) {
            console.error('Error creating vector store:', error);
            throw error;
        }
    }

    static async searchVectors(query) {
        try {
            const response = await axios.post(`${API_URL}/api/vectorstore/search`, {
                query
            });
            return response.data;
        } catch (error) {
            console.error('Error searching vectors:', error);
            throw error;
        }
    }

    static async addDocuments(documents) {
        try {
            const response = await axios.post(`${API_URL}/api/vectorstore/documents`, {
                documents
            });
            return response.data;
        } catch (error) {
            console.error('Error adding documents:', error);
            throw error;
        }
    }
}
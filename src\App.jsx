import React, { useState, useEffect } from "react";
import { Routes, Route, Navigate, useNavigate } from "react-router-dom";
import Sidebar from "./components/Sidebar";
import TopHeader from "./components/TopHeader"; // ✅ Import Top Header
import AppHome from "./pages/AppHome";
import ChatPage from "./pages/ChatPage";
import LoginPage from "./pages/Login";
import NewForm from "./Pages/Forms/AddNewApp";
import { loginUser, fetchAssistantData } from "./services/api"; 
import { AssistantProvider } from "./context/AssistantContext";
import DirectGraph from "./Pages/DirectGraph";
import VisualBuilderPage from "./Pages/Agentbuilder/VisualBuilderPage"

export default function App() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [assistants, setAssistants] = useState([]); 
  const navigate = useNavigate();

  useEffect(() => {
    const fetchUser = async () => {
      try {
        console.log("🔄 Checking login...");
        const data = await loginUser();
        console.log("✅ Login successful:", data);
        setUser(data);
        document.cookie = `user_id=${encodeURIComponent(data.user_id)}; path=/`;
        navigate("/");
      } catch (error) {
        console.error("❌ Login failed:", error);
        let user = { user_id: "Nitin Sinha" };
        document.cookie = `user_id=${encodeURIComponent("Nitin Sinha")}; path=/`;
        setUser(user);
      }
      setLoading(false);
    };

    fetchUser();
  }, [navigate]);

  useEffect(() => {
    if (user) {
      fetchAssistantData()
        .then(setAssistants)
        .catch((err) => console.error("❌ Error fetching assistants:", err));
    }
  }, [user]);

  if (loading) {
    console.log("⏳ Loading login check...");
    return <div>Loading...</div>;
  }

  console.log("🔹 Rendering App - User:", user);

  return (
    <AssistantProvider>
      <div className="app-container">
        {user && <Sidebar user={user} />}
        <div className="main-content">
          {user && <TopHeader />} {/* ✅ Include TopHeader for logged-in users */}
          <Routes>
            <Route path="/" element={user ? <AppHome /> : <Navigate to="/login" />} />
            <Route  path="/chat"  element={user ? (<ChatPage key={location.key} /> ) : (<Navigate to="/login" />)}/>
            <Route path="/login" element={<LoginPage setUser={setUser} />} />
            <Route path="/new-form" element={<NewForm />} />
            <Route path="/graph" element={<DirectGraph />} />
            <Route path="/agentUI" element={<VisualBuilderPage/>}/>
          </Routes>
        </div>
      </div>
    </AssistantProvider>
  );
}


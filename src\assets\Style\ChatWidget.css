
.chat-widget {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Toggle Button */
.chat-toggle-button {
  background-color: #d71921 !important;
  color: white !important;
  width: 60px;
  height: 60px;
  border-radius: 50% !important;
  box-shadow: 0 4px 12px rgba(215, 25, 33, 0.3) !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
  border: none !important;
}

.chat-toggle-button:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(215, 25, 33, 0.4) !important;
}

/* Container for the resizable chat */
.resizable-container {
  position: relative;
}

/* Chat window */
.chat-window {
  background: #ffffff;
  border-radius: 20px;
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid rgba(215, 25, 33, 0.1);
  animation: slideIn 0.3s ease-out;
  transition: width 0.2s ease, height 0.2s ease;
  position: relative;
  max-height: 100vh;
  box-sizing: border-box;
}

/* Resize handle */
.resize-handle {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 20px;
  height: 20px;
  cursor: nwse-resize;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.resize-icon {
  color: rgba(215, 25, 33, 0.6);
  font-size: 14px;
  transform: rotate(45deg);
}

/* Visual indicator when resizing */
.chat-window.resizing {
  border-color: #d71921;
  box-shadow: 0 0 0 2px #d71921;
  transition: none;
}

/* Header */
.chat-header {
  background: #d71921;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.chat-title {
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Chat Body */
.chat-body {
  flex: 1;
  overflow-y: auto;
  transition: height 0.2s ease;
}

.chat-body::-webkit-scrollbar {
  width: 6px;
}

.chat-body::-webkit-scrollbar-track {
  background: transparent;
}

.chat-body::-webkit-scrollbar-thumb {
  background: #d7192133;
  border-radius: 3px;
}

/* Messages Container */
.chat-messages {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Message Wrapper */
.chat-message {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  max-width: 85%;
  animation: fadeIn 0.3s ease-out;
  margin-bottom: 12px;
}

/* User Message */
.chat-message.user {
  justify-content: flex-end;
  margin-left: auto;
  margin-right: 0;
}

/* Bot Message */
.chat-message.bot {
  justify-content: flex-start;
  margin-left: 0;
  margin-right: auto;
}

/* Avatar Styling */
.chat-message .rs-avatar {
  margin-top: 4px;
}

/* Message Text Containers */
.user-message-text {
  background: #d71921;
  color: white;
  padding: 12px 16px;
  border-radius: 16px 16px 4px 16px;
  box-shadow: 0 2px 4px rgba(215, 25, 33, 0.1);
  margin-left: auto;
  word-wrap: break-word;
}

.bot-message-text {
  background: white;
  color: #333;
  padding: 12px 16px;
  border-radius: 16px 16px 16px 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(215, 25, 33, 0.1);
  margin-right: auto;
  word-wrap: break-word;
}

/* Empty Chat State */
.empty-chat {
  text-align: center;
  color: #666;
  margin: 20px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* Flight Cards */
.flight-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid rgba(215, 25, 33, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.flight-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 12px;
}

.flight-detail-block {
  font-size: 14px;
}

/* Input Area */
.chat-footer {
  padding: 16px;
  background: white;
  border-top: 1px solid rgba(215, 25, 33, 0.1);
  display: flex;
  gap: 12px;
  align-items: center;
}

.chat-inputs {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid rgba(215, 25, 33, 0.2);
  border-radius: 24px;
  outline: none;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.chat-inputs:focus {
  border-color: #d71921;
}

/* Chat input section */
.chat-input-section {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  flex-shrink: 0;
  margin-top: auto; /* Push to bottom of flex container */
}

.chat-input {
  width: 100%;
  box-sizing: border-box;
}

.chat-send-button {
  background: #d71921;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.chat-send-button:hover {
  background: #b5141b;
}

/* Date Picker */
.date-picker-container {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(215, 25, 33, 0.1);
  margin: 8px 0;
}

.datepicker-input {
  width: 100%;
  padding: 12px 16px;
  margin: 12px 0;
  border: 2px solid rgba(215, 25, 33, 0.2);
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.datepicker-input:focus {
  border-color: #d71921;
  outline: none;
  box-shadow: 0 0 0 3px rgba(215, 25, 33, 0.1);
}

.date-submit-button {
  background: #d71921;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  transition: background-color 0.2s ease;
  margin-top: 8px;
}

.date-submit-button:hover {
  background: #b5141b;
}

.date-submit-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* PNR Processing Animation */
.pnr-processing-container {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid rgba(215, 25, 33, 0.1);
  border-radius: 16px;
  padding: 20px;
  margin: 12px 0;
  animation: slideIn 0.5s ease-out;
}

.pnr-processing-header {
  text-align: center;
  margin-bottom: 20px;
}

.processing-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.processing-subtitle {
  font-size: 13px;
  color: #64748b;
}

.pnr-processing-steps {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.pnr-processing-step {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  opacity: 0.6;
}

.pnr-processing-step.current {
  opacity: 1;
  background: linear-gradient(135deg, #fef3f2 0%, #fff5f5 100%);
  border: 1px solid rgba(215, 25, 33, 0.2);
  transform: scale(1.02);
}

.pnr-processing-step.complete {
  opacity: 0.8;
  background: linear-gradient(135deg, #f0fdf4 0%, #f7fee7 100%);
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.step-agent-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.agent-avatar {
  width: 36px;
  height: 36px;
  background: #d71921;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 12px;
}

.pnr-processing-step.current .agent-avatar {
  animation: pulse 2s infinite;
}

.pnr-processing-step.complete .agent-avatar {
  background: #22c55e;
}

.agent-details {
  flex: 1;
}

.agent-name {
  font-weight: 600;
  font-size: 14px;
  color: #1e293b;
  margin-bottom: 2px;
}

.agent-message {
  font-size: 13px;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 8px;
}

.typing-dots {
  display: inline-flex;
  align-items: center;
  gap: 2px;
}

.typing-dots .dot {
  width: 4px;
  height: 4px;
  background-color: #d71921;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots .dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-dots .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-4px);
    opacity: 1;
  }
}

.step-status {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.step-complete {
  color: #22c55e;
  font-weight: bold;
  font-size: 16px;
}

.step-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(215, 25, 33, 0.2);
  border-top-color: #d71921;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.step-pending {
  color: #94a3b8;
  font-size: 14px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(215, 25, 33, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(215, 25, 33, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(215, 25, 33, 0);
  }
}

/* Data Stream Animation */
.data-stream-animation {
  margin-top: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  border: 1px dashed rgba(215, 25, 33, 0.2);
}

.data-stream {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.data-line {
  height: 4px;
  background: linear-gradient(90deg, rgba(215, 25, 33, 0.3) 30%, rgba(215, 25, 33, 0.1) 50%, rgba(215, 25, 33, 0.3) 70%);
  border-radius: 2px;
  animation: dataStream 2s infinite linear;
  opacity: 0.7;
}

.data-line:nth-child(1) {
  width: 100%;
}

.data-line:nth-child(2) {
  width: 85%;
  animation-delay: 0.5s;
}

.data-line:nth-child(3) {
  width: 70%;
  animation-delay: 0.3s;
}

.data-line:nth-child(4) {
  width: 90%;
  animation-delay: 0.7s;
}

@keyframes dataStream {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

/* Upload Container */
.enhanced-upload-container {
  width: 100%;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin: 16px 0;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.enhanced-upload-container.hiding {
  opacity: 0;
}

.upload-heading {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
  text-align: left;
}

.upload-box-ui {
  border: 2px dashed rgba(215, 25, 33, 0.2);
  padding: 20px;
  border-radius: 12px;
  background: #f8f9fa;
  transition: border-color 0.3s ease;
}

.upload-box-ui:hover {
  border-color: #d71921;
}

/* Upload Button */
.upload-button {
  display: inline-block;
  cursor: pointer;
  padding: 10px 20px;
  background: white;
  border: 1px solid rgba(215, 25, 33, 0.2);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.upload-button:hover {
  background: #f8f9fa;
  border-color: #d71921;
}

.upload-input {
  display: none;
}

/* File Preview */
.file-preview {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 16px 0;
  padding: 12px;
  background: white;
  border: 1px solid rgba(215, 25, 33, 0.1);
  border-radius: 8px;
  font-size: 14px;
}

.file-icon {
  font-size: 20px;
}

/* Upload Button */
.confirm-upload-btn {
  width: 100%;
  margin-top: 16px;
  padding: 12px;
  background: #d71921;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.confirm-upload-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.confirm-upload-btn:not(:disabled):hover {
  background: #b5141b;
}

/* Upload Progress (optional) */
.upload-progress {
  margin-top: 12px;
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.upload-progress-bar {
  height: 100%;
  background: #d71921;
  transition: width 0.3s ease;
}

/* Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  gap: 4px;
  padding: 12px 16px;
  background: white;
  border-radius: 16px;
  width: fit-content;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  background: #d71921;
  border-radius: 50%;
  animation: bounce 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) { animation-delay: 0.2s; }
.typing-indicator span:nth-child(3) { animation-delay: 0.4s; }

@keyframes bounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

/* Refund Summary */
.refund-summary {
  background: white;
  padding: 16px;
  border-radius: 12px;
  border: 1px solid rgba(215, 25, 33, 0.1);
}

.refund-line {
  margin: 8px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Book Button */
.book-button {
  width: 100%;
  background: #d71921;
  color: white;
  border: none;
  padding: 12px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.book-button:hover {
  background: #b5141b;
}

.processing-indicator {
  padding: 12px;
  background: #f5f5f5;
  border-radius: 8px;
  margin: 8px;
}

.processing-message {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
}

.spinner {
  animation: spin 2s linear infinite;
  display: inline-block;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Itinerary Summary */
.itinerary-summary {
  background: white;
  padding: 16px;
  border-radius: 12px;
  border: 1px solid rgba(215, 25, 33, 0.1);
  margin: 8px 0;
}

.itinerary-container {
  background: #ffffff;
  border-radius: 16px;
  padding: 20px;
  margin: 16px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.itinerary-header {
  margin-bottom: 20px;
}

.itinerary-header h4 {
  margin: 0 0 12px 0;
  color: #1a1a1a;
  font-size: 18px;
}

.booking-info {
  display: flex;
  gap: 20px;
  color: #666;
  font-size: 14px;
}

.booking-info strong {
  color: #1a1a1a;
}

.segments-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.segment-card {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 16px;
  background: #f8f9fa;
}

.segment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
}

.airline-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.airline-logo img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.flight-number {
  color: #666;
  font-size: 14px;
}

.flight-route {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
}

.origin, .destination {
  text-align: center;
}

.city-code {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.datetime {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.flight-line {
  flex: 1;
  position: relative;
  margin: 0 24px;
}

.line {
  height: 2px;
  background: #d71921;
  position: relative;
}

.airplane-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 20px;
}

.refund-confirmation {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}

.refund-warning {
  color: #d71921;
  font-weight: 500;
  margin-bottom: 16px;
}

.refund-details {
  background: #fff3f3;
  border: 1px solid #ffcdd2;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.refund-details p {
  margin: 8px 0;
  color: #333;
}

.refund-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.confirm-refund-btn {
  background: #d71921;
  color: white;
  border: none;
  padding: 10px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.confirm-refund-btn:hover {
  background: #b5141b;
}

.cancel-refund-btn {
  background: white;
  color: #666;
  border: 1px solid #e0e0e0;
  padding: 10px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.cancel-refund-btn:hover {
  background: #f5f5f5;
  border-color: #d0d0d0;
}

/* Add these new styles without modifying existing ones */
.ticket-summary {
  background: #ffffff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #edf2f7;
  max-width: 320px;
  margin: 10px 0;
}

.ticket-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #edf2f7;
}

.ticket-header h4 {
  font-size: 18px;
  color: #2d3748;
  font-weight: 600;
  margin: 0;
}

.ticket-line {
  display: flex;
  align-items: center;
  padding: 6px 0;
  color: #4a5568;
  font-size: 14px;
  line-height: 1.5;
}

.ticket-line strong {
  margin-left: 8px;
  color: #2d3748;
  font-weight: 500;
}

.verify-ticket-btn {
  margin-top: 16px;
  padding: 8px 16px;
  background: #3182ce;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
  width: 100%;
}

.verify-ticket-btn:hover {
  background: #2c5282;
}

/* Enhance existing ticket icons */
.ticket-line span {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

/* Add subtle hover effect on ticket lines */
.ticket-line:hover {
  background: #f7fafc;
  border-radius: 4px;
  padding: 6px 8px;
  margin: 0 -8px;
}

/* Itinerary Styling */
.itinerary-container {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin: 12px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.itinerary-header {
  margin-bottom: 16px;
}

.itinerary-header h4 {
  margin: 0 0 8px 0;
  color: #d71921; /* Virgin Atlantic red */
  font-size: 16px;
}

.booking-info {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 8px;
}

.booking-info span {
  font-size: 14px;
  color: #333;
}

.itinerary-message {
  font-size: 14px;
  color: #666;
  margin: 8px 0;
}

.segments-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.segment-card {
  background-color: white;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #e0e0e0;
}

.segment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.segment-route {
  display: flex;
  align-items: center;
  gap: 8px;
}

.airport-code {
  font-weight: bold;
  font-size: 16px;
}

.route-arrow {
  color: #999;
}

.segment-airline {
  font-size: 14px;
  color: #666;
}

.segment-times {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: 12px;
}

.time-label {
  display: block;
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.time-value {
  font-size: 14px;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

.status-badge.completed {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.cancelled {
  background-color: #f8d7da;
  color: #721c24;
}

.status-badge.pending {
  background-color: #fff3cd;
  color: #856404;
}

.confirmation-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.confirm-button, .cancel-button, .upload-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  border: none;
  transition: background-color 0.2s;
}

.confirm-button {
  background-color: #d71921;
  color: white;
}

.confirm-button:hover {
  background-color: #b5151c;
}

.cancel-button {
  background-color: #e0e0e0;
  color: #333;
}

.cancel-button:hover {
  background-color: #c0c0c0;
}

.upload-suggestion {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px dashed #ccc;
}

.upload-button {
  background-color: #f0f0f0;
  color: #333;
  margin-top: 8px;
}

.upload-button:hover {
  background-color: #e0e0e0;
}

.no-segments {
  padding: 16px;
  text-align: center;
  color: #666;
  font-style: italic;
}

/* PNR Details Styling */
.pnr-details-container {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin: 12px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.pnr-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e0e0;
}

.pnr-header h4 {
  margin: 0;
  color: #d71921; /* Virgin Atlantic red */
  font-size: 18px;
}

.pnr-reference {
  font-size: 14px;
  color: #333;
}

.pnr-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #333;
}

/* Customer Details Styling */
.customer-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.customer-card {
  background-color: white;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #e0e0e0;
}

.customer-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.customer-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 8px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  color: #666;
}

.info-value {
  font-size: 14px;
  color: #333;
}

/* Segment Details Styling */
.segment-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.segment-detail-card {
  background-color: white;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #e0e0e0;
}

.segment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e0e0;
}

.segment-route {
  display: flex;
  align-items: center;
  gap: 8px;
}

.origin, .destination {
  font-weight: bold;
  font-size: 16px;
}

.route-arrow {
  color: #999;
}

.flight-info {
  font-size: 14px;
  color: #666;
}

.segment-details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 8px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-label {
  font-size: 12px;
  color: #666;
}

.detail-value {
  font-size: 14px;
  color: #333;
}

/* Price Details Styling */
.price-card {
  background-color: white;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #e0e0e0;
  text-align: center;
}

.price-amount {
  font-size: 24px;
  font-weight: 600;
  color: #d71921; /* Virgin Atlantic red */
  margin-bottom: 8px;
}

.currency-info {
  font-size: 14px;
  color: #666;
}

.no-data {
  padding: 16px;
  text-align: center;
  color: #666;
  font-style: italic;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

/* Size control buttons */
.chat-controls {
  display: flex;
  align-items: center;
}

.size-controls {
  display: flex;
  margin-right: 10px;
}

.size-btn {
  background: none;
  border: none;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  margin-left: 4px;
  transition: background-color 0.2s;
}

.size-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Resize hint */
.resize-hint {
  position: absolute;
  bottom: 25px;
  right: 25px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s;
}

/* Show the hint when the chat is first opened */
.chat-window:hover .resize-hint {
  opacity: 1;
}

/* Hide the hint after 5 seconds */
@keyframes fadeOut {
  0% { opacity: 1; }
  100% { opacity: 0; }
}

.chat-window .resize-hint {
  animation: fadeOut 0.5s ease-in-out forwards;
  animation-delay: 5s;
}

/* Refund Eligibility Check Styles */
.refund-eligibility-check {
  background: #f8f9fa;
  border: 1px solid rgba(215, 25, 33, 0.1);
  border-radius: 12px;
  padding: 16px;
  margin-top: 16px;
}

.eligibility-question {
  margin-bottom: 12px;
}

.eligibility-question p {
  margin: 0;
  font-weight: 500;
  color: #333;
}

.eligibility-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.check-eligibility-btn {
  background: #d71921;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.check-eligibility-btn:hover {
  background: #b5141b;
}

.skip-eligibility-btn {
  background: transparent;
  color: #666;
  border: 1px solid #ddd;
  padding: 10px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.skip-eligibility-btn:hover {
  background: #f5f5f5;
  border-color: #ccc;
}

/* Refund Eligibility Result Styles */
.refund-eligibility-result {
  background: white;
  border: 1px solid rgba(215, 25, 33, 0.1);
  border-radius: 12px;
  padding: 20px;
  margin: 16px 0;
}

.eligibility-header h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 18px;
}

.eligibility-details {
  margin-bottom: 16px;
}

.eligibility-line {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
}

.eligibility-line:last-child {
  border-bottom: none;
}

.proceed-refund-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  margin-right: 12px;
  transition: background-color 0.2s ease;
}

.proceed-refund-btn:hover {
  background: #218838;
}

.cancel-refund-btn {
  background: transparent;
  color: #666;
  border: 1px solid #ddd;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.cancel-refund-btn:hover {
  background: #f5f5f5;
  border-color: #ccc;
}

/* Table Layout Fixes for Chat Widget */
.chat-widget .rs-table {
  width: 100% !important;
  min-width: auto !important;
  overflow-x: auto !important;
  font-size: 12px !important;
}

.chat-widget .rs-table-cell-content {
  padding: 8px 12px !important;
  font-size: 12px !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

.chat-widget .rs-table-cell-header .rs-table-cell-content {
  font-size: 11px !important;
  font-weight: 600 !important;
  padding: 8px 12px !important;
}

/* Responsive table adjustments for chat widget */
@media (max-width: 600px) {
  .chat-widget .rs-table-cell-content {
    padding: 6px 8px !important;
    font-size: 11px !important;
  }

  .chat-widget .rs-table-cell-header .rs-table-cell-content {
    font-size: 10px !important;
    padding: 6px 8px !important;
  }
}

/* Fix for table container in chat messages */
.chat-message .rs-table-container {
  max-width: 100% !important;
  overflow-x: auto !important;
}

.chat-message .ticket-display {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 12px !important;
  overflow-x: auto !important;
}

.chat-message .controls-container {
  min-width: auto !important;
  flex-wrap: wrap !important;
  gap: 8px !important;
}

.chat-message .search-input {
  width: 200px !important;
  min-width: 150px !important;
}

/* Ensure table wrapper is responsive */
.chat-widget .rs-table-container,
.chat-message .rs-table-container {
  width: 100% !important;
  overflow-x: auto !important;
  -webkit-overflow-scrolling: touch !important;
}

/* Improve table scrolling on mobile */
.chat-widget .rs-table-body-wheel-area {
  overflow-x: auto !important;
  overflow-y: auto !important;
}

/* Better table layout for narrow screens */
@media (max-width: 500px) {
  .chat-message .ticket-display {
    padding: 8px !important;
  }

  .chat-widget .rs-table {
    min-width: 600px !important;
  }

  .chat-widget .custom-table {
    font-size: 11px !important;
  }
}

/* Wrapper for ticket display in chat */
.chat-message .ticket-display-wrapper {
  width: 100% !important;
  max-width: 100% !important;
  overflow-x: auto !important;
  -webkit-overflow-scrolling: touch !important;
}

/* Ensure table doesn't break chat layout */
.chat-message .ticket-display {
  min-width: 600px !important;
  width: auto !important;
  margin: 0 !important;
}

/* Improve horizontal scrolling indicator */
.chat-message .ticket-display-wrapper::-webkit-scrollbar {
  height: 8px;
}

.chat-message .ticket-display-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.chat-message .ticket-display-wrapper::-webkit-scrollbar-thumb {
  background: #d71921;
  border-radius: 4px;
}

.chat-message .ticket-display-wrapper::-webkit-scrollbar-thumb:hover {
  background: #b5141b;
}

/* Streaming Messages Animation */
.streaming-messages-container {
  margin: 16px 0;
  min-height: 60px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  padding: 12px;
  border: 1px dashed rgba(215, 25, 33, 0.2);
}

.streaming-message {
  display: flex;
  align-items: center;
  margin: 4px 0;
  font-size: 13px;
  color: #475569;
  opacity: 0;
  transform: translateX(-20px);
  transition: all 0.4s ease;
}

.streaming-message.appearing {
  opacity: 1;
  transform: translateX(0);
  animation: typewriter 0.8s ease-out;
}

.streaming-message.disappearing {
  opacity: 0;
  transform: translateX(20px);
  animation: fadeOut 0.5s ease-in forwards;
}

.stream-indicator {
  color: #d71921;
  margin-right: 8px;
  font-size: 10px;
  animation: blink 1.5s infinite;
}

.stream-text {
  font-family: 'Courier New', monospace;
  letter-spacing: 0.5px;
}

@keyframes typewriter {
  0% {
    width: 0;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    width: 100%;
    opacity: 1;
  }
}

@keyframes fadeOut {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(20px);
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

/* Scroll to Bottom Button */
.scroll-to-bottom-container {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 10;
  pointer-events: none;
}

.scroll-to-bottom-btn {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(215, 25, 33, 0.2);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  pointer-events: auto;
  color: #d71921;
  backdrop-filter: blur(8px);
  animation: slideUpFadeIn 0.3s ease-out;
}

.scroll-to-bottom-btn:hover {
  background: rgba(215, 25, 33, 0.1);
  border-color: rgba(215, 25, 33, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.scroll-to-bottom-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.scroll-to-bottom-btn svg {
  transition: transform 0.2s ease;
}

.scroll-to-bottom-btn:hover svg {
  transform: translateY(1px);
}

@keyframes slideUpFadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Ensure chat-body has relative positioning for absolute button */
.chat-body {
  position: relative;
}

/* Chatbot-Friendly Date Picker Styles */
.chatbot-date-picker {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  margin: 8px 0;
  max-width: 320px;
  animation: slideUpFadeIn 0.3s ease-out;
}

.date-picker-message {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  color: #475569;
  font-weight: 500;
}

.date-picker-icon {
  font-size: 16px;
}

/* Date Chips - Quick Selection */
.date-chips-container {
  display: flex;
  gap: 6px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.date-chip {
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 70px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.date-chip:hover {
  background: #d71921;
  border-color: #d71921;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(215, 25, 33, 0.2);
}

.chip-label {
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.chip-date {
  font-size: 12px;
  margin-top: 2px;
  opacity: 0.8;
}

/* Compact Date Input */
.date-input-container {
  margin-bottom: 12px;
}

.date-input-label {
  font-size: 12px;
  color: #64748b;
  display: block;
  margin-bottom: 6px;
}

.date-input-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.compact-datepicker-input {
  flex: 1;
  padding: 8px 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  font-size: 13px !important;
  background: white !important;
  font-family: inherit !important;
  transition: border-color 0.2s ease !important;
}

.compact-datepicker-input:focus {
  outline: none !important;
  border-color: #d71921 !important;
  box-shadow: 0 0 0 3px rgba(215, 25, 33, 0.1) !important;
}

.date-confirm-btn {
  background: #d71921;
  color: white;
  border: none;
  border-radius: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.date-confirm-btn:hover {
  background: #b01419;
  transform: scale(1.05);
}

/* Footer */
.date-picker-footer {
  text-align: center;
  padding-top: 8px;
  border-top: 1px solid #e2e8f0;
}

.date-cancel-link {
  background: none;
  border: none;
  color: #64748b;
  font-size: 12px;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.date-cancel-link:hover {
  color: #475569;
}

/* Compact React DatePicker Styles */
.react-datepicker {
  border: 1px solid #d1d5db !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  font-family: inherit !important;
  font-size: 13px !important;
}

.react-datepicker__header {
  background: #d71921 !important;
  border-bottom: none !important;
  border-radius: 7px 7px 0 0 !important;
  color: white !important;
  padding: 8px 0 !important;
}

.react-datepicker__current-month {
  color: white !important;
  font-weight: 600 !important;
  font-size: 14px !important;
}

.react-datepicker__day-names {
  margin-bottom: 4px !important;
}

.react-datepicker__day-name {
  color: rgba(255, 255, 255, 0.8) !important;
  font-weight: 500 !important;
  font-size: 11px !important;
  width: 28px !important;
  line-height: 28px !important;
}

.react-datepicker__month {
  margin: 8px !important;
}

.react-datepicker__week {
  display: flex !important;
}

.react-datepicker__day {
  border-radius: 4px !important;
  transition: all 0.2s ease !important;
  width: 28px !important;
  line-height: 28px !important;
  margin: 1px !important;
  font-size: 12px !important;
}

.react-datepicker__day:hover {
  background: #fee2e2 !important;
  color: #dc2626 !important;
}

.react-datepicker__day--selected {
  background: #d71921 !important;
  color: white !important;
}

.react-datepicker__day--today {
  background: #fef3c7 !important;
  color: #92400e !important;
  font-weight: 600 !important;
}

.react-datepicker__day--keyboard-selected {
  background: #fee2e2 !important;
  color: #dc2626 !important;
}

.react-datepicker__navigation {
  border: none !important;
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 3px !important;
  width: 20px !important;
  height: 20px !important;
  margin-top: 6px !important;
}

.react-datepicker__navigation:hover {
  background: rgba(255, 255, 255, 0.3) !important;
}

.react-datepicker__navigation-icon::before {
  border-color: white !important;
  border-width: 2px 2px 0 0 !important;
  width: 6px !important;
  height: 6px !important;
}

.react-datepicker-popper {
  z-index: 10000 !important;
}

.react-datepicker__triangle {
  border-bottom-color: #d71921 !important;
}

/* Flight Booking Interface Styles */
.flight-booking-interface {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  padding: 20px;
  margin: 12px 0;
  max-width: 450px;
  animation: slideUpFadeIn 0.3s ease-out;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.booking-header {
  text-align: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.booking-header h4 {
  color: #1e293b;
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.booking-header p {
  color: #64748b;
  margin: 0;
  font-size: 14px;
}

.booking-flight-summary {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
}

.flight-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.airline-info {
  display: flex;
  flex-direction: column;
}

.airline-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 14px;
}

.flight-number {
  font-size: 12px;
  color: #64748b;
}

.flight-times {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 13px;
  color: #475569;
}

.flight-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.price-label {
  font-size: 12px;
  color: #64748b;
}

.price-amount {
  font-size: 16px;
  font-weight: 700;
  color: #d71921;
}

.passenger-form {
  margin-bottom: 20px;
}

.passenger-form h5 {
  color: #1e293b;
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.form-row {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.form-input {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #d71921;
  box-shadow: 0 0 0 3px rgba(215, 25, 33, 0.1);
}

.form-input::placeholder {
  color: #9ca3af;
}

.booking-actions {
  display: flex;
  gap: 12px;
  justify-content: space-between;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

.cancel-booking-btn {
  background: #f3f4f6;
  color: #6b7280;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
}

.cancel-booking-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.confirm-booking-btn {
  background: #d71921;
  color: white;
  border: 1px solid #d71921;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 2;
}

.confirm-booking-btn:hover:not(.disabled) {
  background: #b01419;
  border-color: #b01419;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(215, 25, 33, 0.3);
}

.confirm-booking-btn.disabled {
  background: #d1d5db;
  color: #9ca3af;
  border-color: #d1d5db;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Booking Confirmation Interface Styles */
.booking-confirmation-interface {
  background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
  border: 2px solid #bbf7d0;
  border-radius: 16px;
  padding: 24px;
  margin: 12px 0;
  max-width: 450px;
  animation: slideUpFadeIn 0.3s ease-out;
  box-shadow: 0 4px 20px rgba(34, 197, 94, 0.1);
}

.confirmation-header {
  text-align: center;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #bbf7d0;
}

.success-icon {
  font-size: 48px;
  margin-bottom: 12px;
  animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.confirmation-header h4 {
  color: #15803d;
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 700;
}

.confirmation-header p {
  color: #16a34a;
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.confirmation-details {
  margin-bottom: 24px;
}

.booking-ref-section {
  background: white;
  border: 1px solid #bbf7d0;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
  text-align: center;
}

.booking-ref {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.ref-label {
  font-size: 12px;
  color: #16a34a;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.ref-number {
  font-size: 24px;
  font-weight: 700;
  color: #15803d;
  font-family: 'Courier New', monospace;
  letter-spacing: 2px;
  background: #f0fdf4;
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid #bbf7d0;
}

.passenger-section,
.flight-section {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
}

.passenger-section h5,
.flight-section h5 {
  color: #1e293b;
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 1px solid #f1f5f9;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #f8fafc;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row .label {
  font-size: 13px;
  color: #64748b;
  font-weight: 500;
}

.detail-row .value {
  font-size: 13px;
  color: #1e293b;
  font-weight: 600;
  text-align: right;
}

.detail-row .value.price {
  color: #d71921;
  font-size: 16px;
  font-weight: 700;
}

.confirmation-actions {
  border-top: 1px solid #bbf7d0;
  padding-top: 20px;
}

.next-steps {
  margin-bottom: 16px;
}

.next-steps p {
  margin: 8px 0;
  font-size: 13px;
  color: #16a34a;
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.download-ticket-btn,
.manage-booking-btn {
  flex: 1;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.download-ticket-btn {
  background: #d71921;
  color: white;
  border: 1px solid #d71921;
}

.download-ticket-btn:hover {
  background: #b01419;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(215, 25, 33, 0.3);
}

.manage-booking-btn {
  background: white;
  color: #d71921;
  border: 1px solid #d71921;
}

.manage-booking-btn:hover {
  background: #fef2f2;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(215, 25, 33, 0.1);
}

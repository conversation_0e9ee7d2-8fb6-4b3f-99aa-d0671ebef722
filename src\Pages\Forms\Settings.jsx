import React, { useContext, useState } from "react";
import { Drawer, Button, Form, Stack, SelectPicker, Slider, Input } from "rsuite";
import { AssistantContext } from "../../context/AssistantContext"; // ✅ Import Assistant Context

const SettingsView = (props) => {
  const { open, onClose } = props;
  const { assistants, selectedAssistant, setSelectedAssistant, updateAssistant } = useContext(AssistantContext); // ✅ Use context
  const [formValues, setFormValues] = useState(selectedAssistant || {});

  // ✅ Handle Assistant Selection
  const handleAssistantChange = (value) => {
    const selected = assistants.find((a) => a.assistantId === value);
    setSelectedAssistant(selected);
    setFormValues(selected || {});
  };

  // ✅ Handle Form Updates
  const handleChange = (name, value) => {
    setFormValues((prev) => ({ ...prev, [name]: value }));
  };

  // ✅ Save Updated Assistant
  const handleSave = () => {
    updateAssistant(formValues);
    onClose();
  };

  return (
    <Drawer backdrop="static" size="sm" placement="right" open={open} onClose={onClose}>
      <Drawer.Header>
        <Drawer.Title>Settings</Drawer.Title>
        <Drawer.Actions>
          <Button onClick={handleSave} appearance="primary">Save</Button>
          <Button onClick={onClose} appearance="subtle">Cancel</Button>
        </Drawer.Actions>
      </Drawer.Header>

      <Drawer.Body>
        <Form fluid>
          {/* ✅ Assistant Selection */}
          <Form.Group>
            <Form.ControlLabel style={{ fontWeight: '500', fontSize: '18px' }}>Assistant</Form.ControlLabel>
            <SelectPicker
              data={assistants.map((a) => ({ label: a.name, value: a.assistantId }))}
              value={formValues.assistantId}
              onChange={handleAssistantChange}
              block
            />
          </Form.Group>

          {/* ✅ Name */}
          <Form.Group>
            <Form.ControlLabel style={{ fontWeight: '500', fontSize: '18px' }}>Name</Form.ControlLabel>
            <Input name="name" value={formValues.name || ""} onChange={(val) => handleChange("name", val)} />
          </Form.Group>

          {/* ✅ Instructions (Multiline) */}
          <Form.Group>
            <Form.ControlLabel style={{ fontWeight: '500', fontSize: '18px' }}>Instructions</Form.ControlLabel>
            <Input as="textarea" rows={4} name="instructions" value={formValues.instructions || ""} onChange={(val) => handleChange("instructions", val)} />
          </Form.Group>

          {/* ✅ Model Selection */}
          <Form.Group>
            <Form.ControlLabel style={{ fontWeight: '500', fontSize: '18px' }}>Model</Form.ControlLabel>
            <SelectPicker
              data={[
                { label: "GPT-4", value: "gpt-4" },
                { label: "GPT-3.5", value: "gpt-3.5" },
              ]}
              value={formValues.model}
              onChange={(val) => handleChange("model", val)}
              block
            />
          </Form.Group>

          {/* ✅ Temperature Slider */}
          <Form.Group>
            <Form.ControlLabel style={{ fontWeight: '500', fontSize: '18px' }}>Temperature</Form.ControlLabel>
            <Slider progress step={0.1} min={0} max={1} value={formValues.temperature || 0.7} onChange={(val) => handleChange("temperature", val)} />
          </Form.Group>
        </Form>
      </Drawer.Body>
    </Drawer>
  );
};

export default SettingsView;

import React, { createContext, useState, useEffect } from "react";
import { fetchAssistantData } from "../services/api"; // ✅ Ensure API is imported

export const AssistantContext = createContext();

export const AssistantProvider = ({ children }) => {
  const [assistants, setAssistants] = useState([]); // ✅ Store all assistants
  const [selectedAssistant, setSelectedAssistant] = useState(null);

  // ✅ Fetch assistants when component mounts
  useEffect(() => {
    const loadAssistants = async () => {
      try {
        const data = await fetchAssistantData();
        setAssistants(data);
        if (data.length > 0) {
          setSelectedAssistant(data[0]); // ✅ Set first assistant as default
        }
      } catch (error) {
        console.error("Error fetching assistants:", error);
      }
    };

    loadAssistants();
  }, []);

  return (
    <AssistantContext.Provider value={{ assistants, selectedAssistant, setSelectedAssistant }}>
      {children}
    </AssistantContext.Provider>
  );
};

const express = require('express');
const router = express.Router();
const axios = require('axios');
require('dotenv').config();

// Simple in-memory store for vectors
let vectorStore = {
    documents: [],
    embeddings: []
};

// Helper function to get embeddings from OpenAI
async function getEmbeddings(text) {
    try {
        const response = await axios.post(
            'https://api.openai.com/v1/embeddings',
            {
                input: text,
                model: 'text-embedding-ada-002'
            },
            {
                headers: {
                    'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        return response.data.data[0].embedding;
    } catch (error) {
        console.error('Error getting embeddings:', error);
        throw error;
    }
}

// Create vector store
router.post('/', async (req, res) => {
    try {
        const { documents } = req.body;
        
        // Get embeddings for each document
        const embeddings = await Promise.all(
            documents.map(doc => getEmbeddings(doc.text))
        );

        // Store documents and their embeddings
        vectorStore.documents = documents;
        vectorStore.embeddings = embeddings;

        res.json({ success: true, count: documents.length });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Search vectors
router.post('/search', async (req, res) => {
    try {
        const { query } = req.body;
        
        // Get embedding for query
        const queryEmbedding = await getEmbeddings(query);

        // Simple cosine similarity search
        const similarities = vectorStore.embeddings.map((embedding, index) => ({
            similarity: cosineSimilarity(queryEmbedding, embedding),
            document: vectorStore.documents[index]
        }));

        // Sort by similarity and return top 5 results
        const results = similarities
            .sort((a, b) => b.similarity - a.similarity)
            .slice(0, 5);

        res.json(results);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Add documents to existing store
router.post('/documents', async (req, res) => {
    try {
        const { documents } = req.body;
        
        // Get embeddings for new documents
        const newEmbeddings = await Promise.all(
            documents.map(doc => getEmbeddings(doc.text))
        );

        // Add to existing store
        vectorStore.documents.push(...documents);
        vectorStore.embeddings.push(...newEmbeddings);

        res.json({ success: true, count: vectorStore.documents.length });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Helper function for cosine similarity
function cosineSimilarity(vecA, vecB) {
    const dotProduct = vecA.reduce((acc, val, i) => acc + val * vecB[i], 0);
    const normA = Math.sqrt(vecA.reduce((acc, val) => acc + val * val, 0));
    const normB = Math.sqrt(vecB.reduce((acc, val) => acc + val * val, 0));
    return dotProduct / (normA * normB);
}

module.exports = router;
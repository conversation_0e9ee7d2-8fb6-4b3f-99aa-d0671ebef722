import React, { useState, useEffect, useContext } from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Button } from "rsuite";
import { BsQuestionCircle } from "react-icons/bs";
import "./AppHome.css";
import { Search, Plus } from "lucide-react";
import { useNavigate } from "react-router-dom";
import "../App.css";
import { fetchAssistantData, fetchLatestNews } from "../Services/api";
import { AssistantContext } from "../context/AssistantContext";
import { BiHome } from "react-icons/bi";
import { FiHome } from "react-icons/fi";
import { BsPeople } from "react-icons/bs";
import { RiApps2Line } from "react-icons/ri";
import Home from "./home/<USER>";
import Community from "./community/Community";
import MyApps from "./myApps/MyApps";
import { Input, InputGroup } from "rsuite";
import SearchIcon from "@rsuite/icons/Search";

export default function AppHome() {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("Home");
  const [assistantData, setAssistantData] = useState([]);
  const { assistants, setAssistants } = useContext(AssistantContext);
  const [news, setNews] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const assistantArray = await fetchAssistantData();
        setAssistantData(assistantArray);
        setAssistants(assistantArray);

        const latestNews = await fetchLatestNews();
        setNews(latestNews);
      } catch (error) {
        console.error("❌ Fetching Data Error:", error);
      }
    };

    fetchData();
  }, []);

  return (
    <div className="app-container">
      <div className="main-content">
        <h4>Explore apps</h4>
        <p>
          Lorem ipsum dolor sit amet consectetur adipisicing elit. Qui, sequi!
        </p>

        <div className="input-search">
          <InputGroup style={{"height": "40px"}}>
            <Input placeholder="Search" />
            <InputGroup.Button>
              <SearchIcon />
            </InputGroup.Button>
          </InputGroup>
        </div>

        {/* ✅ Tabs Navigation */}
        <div>
          <Tabs
            activeKey={activeTab}
            onSelect={setActiveTab}
            appearance="pills"
            className="custom-tabs"
          >
            <Tabs.Tab eventKey="Home" title="Home" icon={<FiHome />}>
              {" "}
              <Home />
            </Tabs.Tab>

            <Tabs.Tab
              eventKey="Community"
              title="Community"
              icon={<BsPeople />}
            >
              <Community />
            </Tabs.Tab>
            <Tabs.Tab eventKey="MyApps" title="My Apps" icon={<RiApps2Line />}>
              <MyApps />
            </Tabs.Tab>
          </Tabs>
        </div>
      </div>
    </div>
  );
}

import React, { useState } from 'react';
import { <PERSON>, Loader, ButtonGroup, Button, Divider } from 'rsuite';
import { Filter, ArrowUp, ArrowDown } from 'react-feather';
import FlightCard from './FlightCard';

const FlightResults = ({ flights, loading, onSelectFlight }) => {
  const [sortBy, setSortBy] = useState('price');
  const [filterClass, setFilterClass] = useState('all');
  
  // Sort flights based on criteria
  const sortedFlights = [...flights].sort((a, b) => {
    if (sortBy === 'price') {
      return parseFloat(a.price.replace(/[^0-9.-]+/g, '')) - 
             parseFloat(b.price.replace(/[^0-9.-]+/g, ''));
    } else if (sortBy === 'duration') {
      // Calculate duration in minutes for comparison
      const getDurationMinutes = (flight) => {
        const depTime = new Date(`${flight.departureDate}T${flight.departureTime}`);
        const arrTime = new Date(`${flight.arrivalDate}T${flight.arrivalTime}`);
        return (arrTime - depTime) / (1000 * 60);
      };
      return getDurationMinutes(a) - getDurationMinutes(b);
    } else if (sortBy === 'departure') {
      const timeA = a.departureTime.split(':').map(Number);
      const timeB = b.departureTime.split(':').map(Number);
      return timeA[0] * 60 + timeA[1] - (timeB[0] * 60 + timeB[1]);
    }
    return 0;
  });
  
  // Filter flights by class
  const filteredFlights = filterClass === 'all' 
    ? sortedFlights 
    : sortedFlights.filter(flight => 
        flight.ticketClass.toLowerCase().includes(filterClass.toLowerCase())
      );

  return (
    <div className="premium-flight-results">
      <Panel bordered>
        <div className="results-header">
          <h3>Flight Results</h3>
          <div className="results-controls">
            <ButtonGroup>
              <Button 
                appearance={sortBy === 'price' ? 'primary' : 'default'}
                onClick={() => setSortBy('price')}
              >
                Price
              </Button>
              <Button 
                appearance={sortBy === 'duration' ? 'primary' : 'default'}
                onClick={() => setSortBy('duration')}
              >
                Duration
              </Button>
              <Button 
                appearance={sortBy === 'departure' ? 'primary' : 'default'}
                onClick={() => setSortBy('departure')}
              >
                Departure
              </Button>
            </ButtonGroup>
            
            <ButtonGroup>
              <Button 
                appearance={filterClass === 'all' ? 'primary' : 'default'}
                onClick={() => setFilterClass('all')}
              >
                All Classes
              </Button>
              <Button 
                appearance={filterClass === 'economy' ? 'primary' : 'default'}
                onClick={() => setFilterClass('economy')}
              >
                Economy
              </Button>
              <Button 
                appearance={filterClass === 'upper' ? 'primary' : 'default'}
                onClick={() => setFilterClass('upper')}
              >
                Upper Class
              </Button>
            </ButtonGroup>
          </div>
        </div>
        
        <Divider />
        
        {loading ? (
          <div className="loading-container">
            <Loader size="lg" content="Searching for the best flights..." />
          </div>
        ) : (
          <div className="flights-container">
            {filteredFlights.length > 0 ? (
              filteredFlights.map((flight, index) => (
                <FlightCard 
                  key={index} 
                  flight={flight} 
                  onSelect={onSelectFlight} 
                />
              ))
            ) : (
              <div className="no-results">
                <p>No flights match your criteria. Try adjusting your filters.</p>
              </div>
            )}
          </div>
        )}
      </Panel>
    </div>
  );
};

export default FlightResults;
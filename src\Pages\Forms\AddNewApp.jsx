import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Uploader,
  Message,
  toaster,
  SelectPicker,
  Input,
  RadioGroup,
  Radio,
  Modal
} from "rsuite";
import { Table, Column, <PERSON>erCell, Cell } from "rsuite-table";
import "rsuite-table/dist/css/rsuite-table.css";
import { Plus, Upload } from "lucide-react";
import "../../App.css";
import "../../assets/Style/NewForm.css";
import { createAssistantModel } from "../../models/AssistantModel";
import { fetchVectorStores } from "../../Services/api";

// Mock AD Groups & Users for Permissions tab
const MOCK_AD_GROUPS = [
  {
    label: "Engineering Team",
    value: "eng_team",
    children: Array.from({ length: 100 }, (_, i) => ({
      label: `Engineer ${i + 1}`,
      value: `user_engineer_${i + 1}`
    }))
  },
  {
    label: "Finance Team",
    value: "finance_team",
    children: Array.from({ length: 200 }, (_, i) => ({
      label: `Finance User ${i + 1}`,
      value: `user_finance_${i + 1}`
    }))
  },
  {
    label: "HR Team",
    value: "hr_team",
    children: Array.from({ length: 50 }, (_, i) => ({
      label: `HR User ${i + 1}`,
      value: `user_hr_${i + 1}`
    }))
  }
];

/*
MultiGroupPermissionSelector Component
----------------------------------------
A dual-pane UI for managing many AD groups and their users.
(This component remains unchanged.)
*/
function MultiGroupPermissionSelector({ adGroups, onSelectionChange }) {
  const [groupFilter, setGroupFilter] = useState("");
  const [groupSelections, setGroupSelections] = useState({});
  const [panelOpen, setPanelOpen] = useState({});
  const [searchTerms, setSearchTerms] = useState({});

  const toggleGroup = (groupId) => {
    const group = adGroups.find((g) => g.value === groupId);
    const newSelections = { ...groupSelections };
    if (newSelections[groupId]) {
      delete newSelections[groupId];
    } else {
      newSelections[groupId] = group.children.map((user) => user.value);
    }
    setGroupSelections(newSelections);
    updateGlobalSelection(newSelections);
  };

  const updateGroupSelection = (groupId, newUserSelections) => {
    const newSelections = { ...groupSelections, [groupId]: newUserSelections };
    setGroupSelections(newSelections);
    updateGlobalSelection(newSelections);
  };

  const updateGlobalSelection = (selections) => {
    let global = [];
    Object.values(selections).forEach((arr) => {
      global = global.concat(arr);
    });
    onSelectionChange(global);
  };

  const togglePanel = (groupId) => {
    setPanelOpen({ ...panelOpen, [groupId]: !panelOpen[groupId] });
  };

  const renderGroupPanel = (group) => {
    const selectedUsers = groupSelections[group.value] || [];
    const searchTerm = searchTerms[group.value] || "";
    const filteredUsers = group.children.filter((user) =>
      user.label.toLowerCase().includes(searchTerm.toLowerCase())
    );
    return (
      <div
        key={group.value}
        style={{
          border: "1px solid #ccc",
          marginBottom: "8px",
          borderRadius: "4px",
          backgroundColor: "#fff"
        }}
      >
        <div
          style={{
            backgroundColor: "#e9ecef",
            padding: "8px",
            cursor: "pointer",
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            color: "#6c757d",
            fontWeight: "bold"
          }}
          onClick={() => togglePanel(group.value)}
        >
          <span>{group.label} Users</span>
          <span>{panelOpen[group.value] ? "–" : "+"}</span>
        </div>
        {panelOpen[group.value] && (
          <div style={{ padding: "8px" }}>
            <Input
              placeholder="Search users..."
              value={searchTerms[group.value] || ""}
              onChange={(value) =>
                setSearchTerms({ ...searchTerms, [group.value]: value })
              }
              style={{ marginBottom: "8px" }}
            />
            <div>
              {filteredUsers.map((user) => (
                <div
                  key={user.value}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    marginBottom: "4px"
                  }}
                >
                  <input
                    type="checkbox"
                    checked={selectedUsers.includes(user.value)}
                    onChange={(e) => {
                      let newSelection = selectedUsers;
                      if (e.target.checked) {
                        newSelection = [...selectedUsers, user.value];
                      } else {
                        newSelection = selectedUsers.filter(
                          (val) => val !== user.value
                        );
                      }
                      updateGroupSelection(group.value, newSelection);
                    }}
                    style={{
                      marginRight: "8px",
                      accentColor: "#6c757d"
                    }}
                  />
                  <span style={{ color: "#6c757d" }}>{user.label}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const filteredGroups = adGroups.filter((group) =>
    group.label.toLowerCase().includes(groupFilter.toLowerCase())
  );

  return (
    <div>
      <div style={{ marginBottom: "8px" }}>
        <Input
          placeholder="Search groups..."
          value={groupFilter}
          onChange={(value) => setGroupFilter(value)}
        />
      </div>
      <div
        style={{
          borderBottom: "1px solid #ccc",
          paddingBottom: "8px",
          marginBottom: "8px",
          overflowX: "auto",
          whiteSpace: "nowrap"
        }}
      >
        {filteredGroups.map((group) => (
          <label
            key={group.value}
            style={{
              marginRight: "16px",
              cursor: "pointer",
              color: "#6c757d",
              fontWeight: "bold"
            }}
          >
            <input
              type="checkbox"
              checked={groupSelections[group.value] ? true : false}
              onChange={() => toggleGroup(group.value)}
              style={{ marginRight: "4px", accentColor: "#6c757d" }}
            />
            {group.label}
          </label>
        ))}
      </div>
      <div style={{ maxHeight: "400px", overflowY: "auto" }}>
        {adGroups
          .filter((group) => groupSelections[group.value])
          .map(renderGroupPanel)}
      </div>
    </div>
  );
}

export default function NewForm() {
  const [activeKey, setActiveKey] = useState("vectorStore");
  const [isVectorStoreCreated, setVectorStoreCreated] = useState(false);

  // Vector Store states
  const [vectorStoreMode, setVectorStoreMode] = useState("create");
  const [vectorStoreName, setVectorStoreName] = useState("");
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [selectedVectorStore, setSelectedVectorStore] = useState("");

  // Assistant states
  const [assistantName, setAssistantName] = useState("");
  const [provider, setProvider] = useState("");
  const [model, setModel] = useState("");
  const [instructions, setInstructions] = useState("");
  const [temperature, setTemperature] = useState(0.7);

  // Permissions tab
  const [finalSelectedUsers, setFinalSelectedUsers] = useState([]);

  // Vector stores for "Select Existing" mode
  const [vectorStores, setVectorStores] = useState([]);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(25);
  const [selectedRow, setSelectedRow] = useState(null);

  // Modal state for Attach popup
  const [showAttachModal, setShowAttachModal] = useState(false);

  // Load vector stores from API when in "select" mode
  useEffect(() => {
    if (vectorStoreMode === "select") {
      fetchVectorStores()
        .then((data) => setVectorStores(data))
        .catch((error) => console.error("Error fetching vector stores:", error));
    }
  }, [vectorStoreMode]);

  const total = vectorStores.length;
  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  const paginatedStores = vectorStores.slice(start, end);

  const handleCreateVectorStore = async () => {
    if (vectorStoreMode === "create") {
      if (!vectorStoreName.trim() || uploadedFiles.length === 0) {
        alert("Please provide a Vector Store name and upload at least one file.");
        return;
      }

      try {
        const newVectorStore = await createVectorStore(vectorStoreName, uploadedFiles);
        setVectorStoreCreated(true);
        setActiveKey("assistant");
        setShowAttachModal(false);
        toaster.push(
          <Message type="success" showIcon>
            Vector Store created successfully!
          </Message>,
          { placement: "topCenter" }
        );
        return newVectorStore.vectorStoreId;
      } catch (error) {
        toaster.push(
          <Message type="error" showIcon>
            Failed to create Vector Store: {error.message}
          </Message>,
          { placement: "topCenter" }
        );
        return null;
      }
    } else {
      if (!selectedVectorStore) {
        alert("Please select an existing vector store.");
        return;
      }
      
      if (uploadedFiles.length > 0) {
        try {
          await addDocumentsToVectorStore(selectedVectorStore, uploadedFiles);
          toaster.push(
            <Message type="success" showIcon>
              Documents added to Vector Store successfully!
            </Message>,
            { placement: "topCenter" }
          );
        } catch (error) {
          toaster.push(
            <Message type="error" showIcon>
              Failed to add documents: {error.message}
            </Message>,
            { placement: "topCenter" }
          );
        }
      }

      setVectorStoreCreated(true);
      setActiveKey("assistant");
      return selectedVectorStore;
    }
  };

  const handleSaveAssistant = async () => {
    const storeId = await handleCreateVectorStore();
    if (!storeId) return;

    const newAssistant = createAssistantModel({
      assistantId: "",
      name: assistantName,
      provider: provider,
      model: model,
      temperature: temperature,
      userIds: finalSelectedUsers,
      vectorStoreIds: [storeId],
      current: true,
      default: true,
      instructions: instructions
    });
    console.log("New Assistant Model:", newAssistant);
  };

  // Render RSuite Table for "Select Existing" vector stores
  const renderVectorStoreTable = () => {
    return (
      <>
        <div style={{ marginBottom: "8px" }}>
          <Button
            appearance="primary"
            style={{ marginRight: "8px" }}
            onClick={() => setShowAttachModal(true)}
          >
            Add store
          </Button>
          <Button
            appearance="default"
            style={{ marginRight: "8px" }}
            onClick={() => {
              fetchVectorStores()
                .then((data) => setVectorStores(data))
                .catch((error) =>
                  console.error("Error refreshing vector stores:", error)
                );
            }}
          >
            Refresh
          </Button>
          <Button appearance="default" onClick={() => alert("Delete clicked!")} disabled={!selectedRow}>
            Delete
          </Button>
        </div>
        <Table
          data={paginatedStores}
          autoHeight
          onRowClick={(rowData) => {
            setSelectedRow(rowData?.vectorStoreId);
            setSelectedVectorStore(rowData?.vectorStoreId);
          }}
          rowClassName={(rowData) => {
            if (!rowData || !rowData.vectorStoreId) return "";
            return rowData.vectorStoreId === selectedRow ? "table-row-selected" : "";
          }}
        >
          <Column width={50} align="center">
            <HeaderCell style={{ fontWeight: "bold", padding: "8px" }}>Select</HeaderCell>
            <Cell style={{ padding: "8px" }}>
              {(rowData) => (
                <input
                  type="radio"
                  name="vectorStoreRadio"
                  checked={rowData.vectorStoreId === selectedRow}
                  onChange={() => {
                    setSelectedRow(rowData.vectorStoreId);
                    setSelectedVectorStore(rowData.vectorStoreId);
                  }}
                />
              )}
            </Cell>
          </Column>
          <Column width={220} align="left" fixed>
            <HeaderCell style={{ fontWeight: "bold", padding: "8px" }}>ID</HeaderCell>
            <Cell style={{ padding: "8px" }}>
              {(rowData) => (
                <a href="#" onClick={(e) => e.preventDefault()}>
                  {rowData.vectorStoreId}
                </a>
              )}
            </Cell>
          </Column>
          <Column width={200} resizable>
            <HeaderCell style={{ fontWeight: "bold", padding: "8px" }}>Name</HeaderCell>
            <Cell style={{ padding: "8px" }}>
              {(rowData) => (
                <a href="#" onClick={(e) => e.preventDefault()}>
                  {rowData.name}
                </a>
              )}
            </Cell>
          </Column>
          <Column width={100} resizable>
            <HeaderCell style={{ fontWeight: "bold", padding: "8px" }}>Size</HeaderCell>
            <Cell style={{ padding: "8px" }} dataKey="size">
              {(rowData) => rowData.size || "0 B"}
            </Cell>
          </Column>
          <Column width={120} resizable>
            <HeaderCell style={{ fontWeight: "bold", padding: "8px" }}>Status</HeaderCell>
            <Cell style={{ padding: "8px" }} dataKey="status">
              {(rowData) => rowData.status || "Completed"}
            </Cell>
          </Column>
          <Column width={150} resizable>
            <HeaderCell style={{ fontWeight: "bold", padding: "8px" }}>Created</HeaderCell>
            <Cell style={{ padding: "8px" }} dataKey="createdDate">
              {(rowData) => rowData.createdDate || "Mar 2"}
            </Cell>
          </Column>
        </Table>
        <div style={{ display: "flex", justifyContent: "space-between", marginTop: "8px" }}>
          <div>
            <SelectPicker
              data={[
                { label: "5 / Page", value: 5 },
                { label: "10 / Page", value: 10 },
                { label: "25 / Page", value: 25 },
                { label: "50 / Page", value: 50 }
              ]}
              searchable={false}
              value={pageSize}
              onChange={(val) => {
                setPageSize(val);
                setPage(1);
              }}
              style={{ width: 120, marginRight: "8px" }}
            />
          </div>
          <div>
            <Button
              appearance="default"
              size="sm"
              disabled={page === 1}
              onClick={() => setPage((prev) => prev - 1)}
              style={{ marginRight: "8px" }}
            >
              Prev
            </Button>
            <Button
              appearance="default"
              size="sm"
              disabled={end >= total}
              onClick={() => setPage((prev) => prev + 1)}
            >
              Next
            </Button>
          </div>
        </div>
        {renderAttachModal()}
      </>
    );
  };

  // Attach Modal Popup (with uploader and grid below for file details)
  const renderAttachModal = () => {
    return (
      <Modal open={showAttachModal} onClose={() => setShowAttachModal(false)} size="lg">
        <Modal.Header>
          <Modal.Title>Attach files to the assistant file search</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {/* Top row: SelectPicker and read-only Input */}
          <div style={{ display: "flex", gap: "16px", marginBottom: "16px" }}>
            <div style={{ flex: 1 }}>
              <label style={{ display: "block", marginBottom: "8px" }}>Name</label>
              <SelectPicker
                data={[
                  { label: "Create a new vector store", value: "new" },
                  { label: "Some existing store", value: "existing" }
                ]}
                defaultValue="new"
                block
              />
            </div>
            <div style={{ flex: 1 }}>
              <label style={{ display: "block", marginBottom: "8px" }}>Name</label>
              <Input value="AssistantVectorStore_7051" readOnly />
            </div>
          </div>

          {/* Grid/Table for file details */}
          <div style={{ marginTop: "16px" }}>
            <table style={{ width: "100%", borderCollapse: "collapse" }}>
              <thead>
                <tr style={{ backgroundColor: "#f8f9fa" }}>
                  <th style={{ padding: "8px", textAlign: "left", fontWeight: "bold" }}>Name</th>
                  <th style={{ padding: "8px", textAlign: "left", fontWeight: "bold" }}>Status</th>
                  <th style={{ padding: "8px", textAlign: "left", fontWeight: "bold" }}>Error</th>
                  <th style={{ padding: "8px", textAlign: "left", fontWeight: "bold" }}>Size</th>
                  <th style={{ padding: "8px", textAlign: "left", fontWeight: "bold" }}>File type</th>
                  <th style={{ padding: "8px", textAlign: "left", fontWeight: "bold" }}>Uploaded</th>
                </tr>
              </thead>
              <tbody>
                {uploadedFiles.length === 0 ? (
                  <tr>
                    <td colSpan="6" style={{ textAlign: "center", padding: "40px" }}>
                      <div style={{ color: "#999" }}>
                        <svg
                          width="64"
                          height="64"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          viewBox="0 0 24 24"
                          style={{ marginBottom: "16px" }}
                        >
                          <path d="M7.5 19.5h9a4.5 4.5 0 000-9h-.75a6 6 0 00-11.662.75 3.75 3.75 0 00.412 7.486h3z" />
                        </svg>
                        <div style={{ fontSize: "16px" }}>
                          No files have been uploaded yet.
                        </div>
                      </div>
                    </td>
                  </tr>
                ) : (
                  uploadedFiles.map((file, index) => (
                    <tr key={index}>
                      <td style={{ padding: "8px" }}>{file.name}</td>
                      <td style={{ padding: "8px" }}>Uploaded</td>
                      <td style={{ padding: "8px" }}>-</td>
                      <td style={{ padding: "8px" }}>{file.size || "N/A"}</td>
                      <td style={{ padding: "8px" }}>{file.type || "N/A"}</td>
                      <td style={{ padding: "8px" }}>Now</td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Upload section placed below the grid */}
          <div className="file-upload-container" style={{ marginTop: "16px" }}>
            <Uploader
              fileList={uploadedFiles}
              onChange={setUploadedFiles}
              draggable
              multiple
              action=""
              className="custom-uploader"
            >
              <div className="upload-area gray-background" style={{ padding: "16px", textAlign: "center" }}>
                <Upload size={40} />
                <p className="upload-text">Click or Drag files to upload</p>
              </div>
            </Uploader>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={() => setShowAttachModal(false)} appearance="subtle">
            Cancel
          </Button>
          <Button
            onClick={() => {
              handleCreateVectorStore();
              setShowAttachModal(false);
            }}
            appearance="primary"
          >
            Save
          </Button>
        </Modal.Footer>
      </Modal>
    );
  };

  return (
    <div className="app-container">
      <div className="main-content perplexity-form-wrapper">
        {/* Top Header */}
        <div className="top-header">
          <div className="header-left">
            <h1 className="page-title">Create New Assistant</h1>
          </div>
          <div className="header-controls">
            <button
              className="save-btn"
              onClick={handleSaveAssistant}
              disabled={!isVectorStoreCreated}
            >
              <Plus size={16} style={{ marginRight: 6 }} /> Save Assistant
            </button>
          </div>
        </div>
        {/* Tabs */}
        <div className="settings-container">
          <div className="settings-tabs-card fixed-tabs">
            <Tabs
              activeKey={activeKey}
              onSelect={(k) => isVectorStoreCreated && setActiveKey(k)}
              className="custom-tabs"
              appearance="subtle"
              vertical
            >
              <Tabs.Tab eventKey="vectorStore" title="Vector Store">
                <p className="tab-description">
                  Vector stores allow AI to reference large datasets for better responses.
                </p>
              </Tabs.Tab>
              <Tabs.Tab eventKey="assistant" title="Assistant" disabled={!isVectorStoreCreated}>
                <p className="tab-description">
                  Define assistant details like name, provider, and instructions.
                </p>
              </Tabs.Tab>
              <Tabs.Tab eventKey="settings" title="Settings" disabled={!isVectorStoreCreated}>
                <p className="tab-description">
                  Configure AI behavior, response temperature, and model settings.
                </p>
              </Tabs.Tab>
              <Tabs.Tab eventKey="permissions" title="Permissions" disabled={!isVectorStoreCreated}>
                <p className="tab-description">
                  Select AD Groups and users.
                </p>
              </Tabs.Tab>
            </Tabs>
          </div>
          <div className="settings-content-card expanded-panel">
            {activeKey === "vectorStore" && (
              <Panel className="form-panel">
                <h2 className="section-heading">Vector Store</h2>
                <div style={{ marginBottom: "16px" }}>
                  <RadioGroup inline value={vectorStoreMode} onChange={setVectorStoreMode}>
                    <Radio value="select">Select Existing</Radio>
                    <Radio value="create">Create New</Radio>
                  </RadioGroup>
                </div>
                {vectorStoreMode === "select" ? (
                  <>
                    {renderVectorStoreTable()}
                    <Button
                      appearance="primary"
                      block
                      onClick={handleCreateVectorStore}
                      style={{ marginTop: "16px" }}
                    >
                      Use Selected Vector Store
                    </Button>
                  </>
                ) : (
                  <>
                    <div className="settings-item">
                      <label className="settings-label">Store Name</label>
                      <Input
                        placeholder="Enter Vector Store Name"
                        className="perplexity-input"
                        value={vectorStoreName}
                        onChange={setVectorStoreName}
                      />
                    </div>
                    <div className="file-upload-container">
                      <Uploader
                        fileList={uploadedFiles}
                        onChange={setUploadedFiles}
                        draggable
                        multiple
                        action=""
                        className="custom-uploader"
                      >
                        <div className="upload-area gray-background" style={{ padding: "16px", textAlign: "center" }}>
                          <Upload size={40} />
                          <p className="upload-text">Click or Drag files to upload</p>
                        </div>
                      </Uploader>
                    </div>
                    <Button appearance="primary" block onClick={handleCreateVectorStore}>
                      Create Vector Store
                    </Button>
                  </>
                )}
              </Panel>
            )}
            {activeKey === "assistant" && (
              <Panel className="form-panel">
                <h2 className="section-heading">Assistants</h2>
                <div className="settings-item">
                  <label className="settings-label">Load Existing Assistant</label>
                  <SelectPicker
                    data={[{ label: "-- Create New Assistant --", value: "" }]}
                    searchable={false}
                    cleanable={false}
                    className="perplexity-input"
                    placeholder="-- Create New Assistant --"
                  />
                </div>
                <h2 className="section-heading">Create New Assistant</h2>
                <div className="settings-item">
                  <label className="settings-label">Provider *</label>
                  <SelectPicker
                    data={[
                      { label: "Select provider", value: "" },
                      { label: "Azure Open AI", value: "azure" },
                      { label: "Test Provider", value: "test" }
                    ]}
                    searchable={false}
                    className="perplexity-input"
                    value={provider}
                    onChange={setProvider}
                  />
                </div>
                <div className="settings-item-row">
                  <div className="settings-item">
                    <label className="settings-label">Name *</label>
                    <Input
                      placeholder="Enter assistant name"
                      className="perplexity-input"
                      value={assistantName}
                      onChange={setAssistantName}
                    />
                  </div>
                  <div className="settings-item">
                    <label className="settings-label">Model *</label>
                    <Input
                      placeholder="Enter model"
                      className="perplexity-input"
                      value={model}
                      onChange={setModel}
                    />
                  </div>
                </div>
                <div className="settings-item">
                  <label className="settings-label">Instructions *</label>
                  <Input
                    as="textarea"
                    rows={3}
                    placeholder="Enter instructions..."
                    className="perplexity-input"
                    value={instructions}
                    onChange={setInstructions}
                  />
                </div>
              </Panel>
            )}
            {activeKey === "permissions" && (
              <Panel className="form-panel">
                <h2 className="section-heading">User Permissions</h2>
                <div className="settings-item">
                  <label className="settings-label">Assign Users</label>
                  <p className="item-subtitle">
                    Use the panels below to select AD groups and then individual users.
                  </p>
                </div>
                <MultiGroupPermissionSelector
                  adGroups={MOCK_AD_GROUPS}
                  onSelectionChange={setFinalSelectedUsers}
                />
              </Panel>
            )}
          </div>
        </div>
      </div>
      {/* Attach Modal Popup */}
      {renderAttachModal()}
    </div>
  );
}


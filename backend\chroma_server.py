import chromadb
from chromadb.config import Settings
import os

# Create storage directory if it doesn't exist
storage_path = os.path.join(os.path.dirname(__file__), "chroma_storage")
os.makedirs(storage_path, exist_ok=True)

# Initialize ChromaDB client with persistent storage
client = chromadb.Client(Settings(
    chroma_server_host="localhost",
    chroma_server_http_port=8000,
    persist_directory=storage_path,
    is_persistent=True
))

print("ChromaDB server running on http://localhost:8000")
print("Storage path:", storage_path)
print("Press Enter to exit...")

# Keep the server running
input()

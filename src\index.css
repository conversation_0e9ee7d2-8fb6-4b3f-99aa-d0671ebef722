@import "rsuite/dist/rsuite.min.css";

/* Sidebar Styling */
.sidebar {
  width: 260px;
  /* height: 100vh; */
  background: #1a1a1a;
  color: white;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.sidebar h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 20px;
}

.sidebar .rs-nav {
  width: 100%;
}

.sidebar .rs-nav-item {
  padding: 10px;
  color: #ddd;
}

.sidebar .rs-nav-item:hover {
  background-color: #333;
  color: white;
}

/* Chatbot Main Screen */
.chat-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100vh;
  background: #f5f5f5;
  padding: 20px;
}

/* Search Bar */
.search-bar {
  display: flex;
  align-items: center;
  background: white;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-bar input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 14px;
  padding: 5px;
}

/* App Cards */
.app-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.app-card {
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.app-card:hover {
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
}

.app-card h3 {
  font-size: 1rem;
  font-weight: bold;
}

.app-card p {
  font-size: 0.85rem;
  color: gray;
}

/* Sidebar Search */
.sidebar-search {
  position: relative;
  margin-bottom: 20px;
  padding: 0 8px;
}

.sidebar-search input {
  width: 100%;
  padding: 8px 32px;
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background: var(--search-bg);
  font-size: 14px;
  color: var(--text-color);
}

.sidebar-search .search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--muted-text);
}

/* Sidebar Sections */
.sidebar-sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.section-header {
  font-size: 12px;
  text-transform: uppercase;
  color: var(--muted-text);
  padding: 0 8px;
  margin-bottom: 4px;
}

.section-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-color);
}

.section-item:hover {
  background-color: rgba(215, 25, 33, 0.05);
}

.section-item.active {
  background-color: rgba(215, 25, 33, 0.1);
  color: var(--accent-color);
}

.item-text {
  flex: 1;
  font-size: 14px;
}

.shortcut {
  font-size: 12px;
  color: var(--muted-text);
  background: rgba(0, 0, 0, 0.05);
  padding: 2px 4px;
  border-radius: 4px;
}

/* Chat History Items */
.chat-history-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.chat-history-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chat-history-item:hover {
  background-color: rgba(215, 25, 33, 0.05);
}

.chat-preview {
  flex: 1;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.chat-history-item:hover .chat-actions {
  opacity: 1;
}

.action-button {
  background: transparent !important;
  padding: 4px !important;
  min-width: unset !important;
  color: var(--muted-text) !important;
}

.action-button:hover {
  color: var(--accent-color) !important;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}


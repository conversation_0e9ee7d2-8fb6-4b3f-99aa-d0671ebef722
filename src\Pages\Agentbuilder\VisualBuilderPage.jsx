// pages/visual-builder.jsx

import React, { useCallback, useState } from "react";
import ReactFlow, {
  addEdge,
  Background,
  Controls,
  MiniMap,
  useEdgesState,
  useNodesState,
  ReactFlowProvider,
} from "reactflow";
import "reactflow/dist/style.css";
import CustomNode from "../../components/CustomNode";
import "../../assets/Style/VisualBuilderPage.css";

const componentLibrary = {
  Agents: ["AssistantAgent", "Web Surfer Agent", "Verification Assistant", "UserProxyAgent"],
  Models: ["gpt-4o-mini", "gpt-3.5-turbo", "custom-llm", "fast-inference"],
  Tools: ["calculator", "fetch_webpage", "flight_search", "email_parser"],
  Terminations: ["OrTerminationCondition", "AndTerminationCondition", "TimeoutCondition"],
};

const initialNodes = [
  {
    id: "1",
    type: "custom",
    position: { x: 250, y: 5 },
    data: { label: "AssistantAgent", agents: [], tools: [], model: null, onDrop: () => {} },
  },
  {
    id: "2",
    type: "custom",
    position: { x: 100, y: 200 },
    data: { label: "RoundRobin Team", agents: [], terminations: [], onDrop: () => {} },
  },
];

const initialEdges = [
  { id: "e1-2", source: "1", target: "2", label: "connects to" },
];

function VisualBuilderPage() {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [reactFlowInstance, setReactFlowInstance] = useState(null);
  const [expanded, setExpanded] = useState({ Agents: true, Models: true, Tools: true, Terminations: true });

  const handleNodeDrop = (id, field, item) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id !== id) return node;
        const updated = { ...node };
        if (field === "model") {
          updated.data.model = item;
        } else if (!updated.data[field].includes(item)) {
          updated.data[field] = [...updated.data[field], item];
        }
        return updated;
      })
    );
  };

  const onConnect = useCallback(
    (params) => setEdges((eds) => addEdge({ ...params, animated: true }, eds)),
    []
  );

  const onDragStart = (event, nodeType, label) => {
    event.dataTransfer.setData("application/reactflow/component", JSON.stringify({ nodeType, label }));
    event.dataTransfer.effectAllowed = "move";
  };

  const onDrop = useCallback(
    (event) => {
      event.preventDefault();
      const rawData = event.dataTransfer.getData("application/reactflow/component");
      if (!rawData) return;
      const data = JSON.parse(rawData);

      const bounds = event.target.getBoundingClientRect();
      const position = reactFlowInstance.project({
        x: event.clientX - bounds.left,
        y: event.clientY - bounds.top,
      });

      const newNode = {
        id: `${+new Date()}`,
        type: "custom",
        position,
        data: {
          label: data.label,
          agents: [],
          terminations: [],
          tools: [],
          model: null,
          onDrop: handleNodeDrop,
        },
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [reactFlowInstance]
  );

  const onDragOver = useCallback((event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  }, []);

  const toggleSection = (section) => {
    setExpanded((prev) => ({ ...prev, [section]: !prev[section] }));
  };

  return (
    <div className="visual-builder-wrapper">
      <div className="sidebar">
        <h3 className="library-title">Component Library</h3>
        <p className="library-subtitle">Drag a component to add it to the team</p>
        <input type="text" placeholder="Search components..." className="component-search" />

        {Object.entries(componentLibrary).map(([section, items]) => (
          <div key={section} className="library-section">
            <div className="section-header" onClick={() => toggleSection(section)}>
              <span className="collapse-arrow">{expanded[section] ? "▾" : "▸"}</span> <strong>{section}</strong> <span className="item-count">({items.length})</span>
            </div>
            {expanded[section] && (
              <div className="section-items">
                {items.map((item) => (
                  <div
                    key={item}
                    className="draggable-item"
                    draggable
                    onDragStart={(e) => onDragStart(e, section.slice(0, -1), item)}
                  >
                    <span className="item-icon">📦</span> {item}
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="flow-canvas">
        <ReactFlow
          nodes={nodes.map((n) => ({ ...n, data: { ...n.data, onDrop: handleNodeDrop } }))}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onDrop={onDrop}
          onDragOver={onDragOver}
          onInit={setReactFlowInstance}
          fitView
          nodeTypes={{ custom: CustomNode }}
        >
          <MiniMap />
          <Controls />
          <Background color="#eee" gap={20} />
        </ReactFlow>
      </div>
    </div>
  );
}

export default function WrappedPage() {
  return (
    <ReactFlowProvider>
      <VisualBuilderPage />
    </ReactFlowProvider>
  );
}
